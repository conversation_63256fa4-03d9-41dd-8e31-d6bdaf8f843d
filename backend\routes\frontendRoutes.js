const express = require('express');
const router = express.Router();
const path = require('path');

// API Routes untuk frontend
router.get('/videos', require('../controller/postController').getVideos);
router.get('/videos/:id', require('../controller/postController').getVideoById);
router.post('/videos/:id/like', require('../controller/postController').toggleVideoLike);
router.post('/videos/:id/share', require('../controller/postController').incrementVideoShare);
router.post('/videos/:id/view', require('../controller/postController').incrementVideoView);

// News/Posts routes
router.get('/posts', require('../controller/postController').getPosts);
router.get('/posts/:id', require('../controller/postController').getPostById);
router.post('/posts/:id/share', require('../controller/postController').incrementShare);

// Settings routes
router.get('/settings', (req, res) => {
  // Proxy ke PHP API untuk settings
  res.redirect('/admin/api/get_settings');
});

// Categories routes
router.get('/categories', (req, res) => {
  // Return categories data
  const categories = [
    { id: 1, name: 'Umum', color: '#6B7280' },
    { id: 2, name: 'Teknologi', color: '#3B82F6' },
    { id: 3, name: 'Bisnis', color: '#10B981' },
    { id: 4, name: 'Olahraga', color: '#F59E0B' },
    { id: 5, name: 'Hiburan', color: '#EF4444' },
    { id: 6, name: 'Politik', color: '#8B5CF6' },
    { id: 7, name: 'Kesehatan', color: '#06B6D4' }
  ];
  res.json({ success: true, data: categories });
});

// Saved posts routes
router.get('/saved', (req, res) => {
  // Return dummy saved posts for now
  const savedPosts = [
    {
      id: 1,
      title: 'Berita Tersimpan 1',
      content: 'Ini adalah contoh berita yang telah disimpan oleh user.',
      image: 'https://source.unsplash.com/400x300/?news',
      category: 'Teknologi',
      category_name: 'Teknologi',
      category_color: '#3B82F6',
      date: new Date().toISOString(),
      created_at: new Date().toISOString(),
      views: 150,
      share: 25,
      likes: 45
    }
  ];
  res.json({ success: true, data: savedPosts });
});

// Health check
router.get('/health', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Frontend API is working!', 
    timestamp: new Date().toISOString(),
    port: process.env.PORT || 3000
  });
});

module.exports = router;
