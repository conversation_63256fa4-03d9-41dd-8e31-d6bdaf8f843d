import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Avatar,
  Chip,
  useTheme,
  useMediaQuery
} from '@mui/material';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import ShareIcon from '@mui/icons-material/Share';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';

// Video data will be loaded from database via API

// Individual Video Item Component
function VideoItem({ video, isActive, onVideoClick }) {
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [likes, setLikes] = useState(video.stats.likes);
  const iframeRef = useRef(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // TikTok aspect ratio: 9:16 (vertical)
  const videoHeight = isMobile ? '100vh' : '80vh';
  const videoWidth = isMobile ? '100vw' : '45vh'; // 9:16 ratio

  // YouTube iframe API for better control
  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {
    const params = new URLSearchParams({
      autoplay: autoplay ? '1' : '0',
      mute: '1',
      controls: '0',
      loop: '1',
      playlist: videoId,
      rel: '0',
      showinfo: '0',
      modestbranding: '1',
      iv_load_policy: '3',
      fs: '0',
      disablekb: '1'
    });
    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;
  };

  const handleVideoClick = async () => {
    // For YouTube iframe, we'll just show interaction feedback
    console.log('Video clicked:', video.title);

    try {
      // Try Express API first, fallback to PHP API
      try {
        await fetch(`http://localhost:8000/api/videos/${video.id}/view`, {
          method: 'POST',
          signal: AbortSignal.timeout(1000)
        });
      } catch (expressError) {
        // Fallback to PHP API
        await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views&id=${video.id}`, {
          method: 'POST'
        });
      }
    } catch (error) {
      console.error('Failed to update view count:', error);
    }
  };

  const handleLike = async () => {
    const wasLiked = isLiked;

    // Optimistic update
    setIsLiked(!isLiked);
    setLikes(prev => isLiked ? prev - 1 : prev + 1);

    try {
      // Try Express API first, fallback to PHP API
      let response;
      let data;

      try {
        response = await fetch(`http://localhost:8000/api/videos/${video.id}/like`, {
          method: 'POST',
          signal: AbortSignal.timeout(1000)
        });
        data = await response.json();
      } catch (expressError) {
        // Fallback to PHP API
        response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like&id=${video.id}`, {
          method: 'POST'
        });
        data = await response.json();
      }

      if (data.success) {
        // Update with server response
        setIsLiked(data.liked);
        setLikes(data.likes);
      } else {
        // Revert on failure
        setIsLiked(wasLiked);
        setLikes(prev => wasLiked ? prev + 1 : prev - 1);
      }
    } catch (error) {
      console.error('Failed to toggle video like:', error);
      // Revert on error
      setIsLiked(wasLiked);
      setLikes(prev => wasLiked ? prev + 1 : prev - 1);
    }
  };

  const handleSave = () => {
    setIsSaved(!isSaved);
  };

  const handleShare = async () => {
    console.log('Share video:', video.id);

    try {
      // Try Express API first, fallback to PHP API
      let response;
      let data;

      try {
        response = await fetch(`http://localhost:8000/api/videos/${video.id}/share`, {
          method: 'POST',
          signal: AbortSignal.timeout(1000)
        });
        data = await response.json();
      } catch (expressError) {
        // Fallback to PHP API
        response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share&id=${video.id}`, {
          method: 'POST'
        });
        data = await response.json();
      }

      if (data.success) {
        // Update local share count
        video.stats.shares = data.shares;
      }
    } catch (error) {
      console.error('Failed to update share count:', error);
    }
  };

  const handleComment = () => {
    console.log('Open comments for video:', video.id);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 hari lalu';
    if (diffDays < 7) return `${diffDays} hari lalu`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;
    return `${Math.ceil(diffDays / 30)} bulan lalu`;
  };

  return (
    <Box sx={{
      position: 'relative',
      height: videoHeight,
      width: '100%',
      maxWidth: videoWidth,
      mx: 'auto',
      bgcolor: '#000',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden'
    }}>
      {/* YouTube Video Iframe */}
      <iframe
        ref={iframeRef}
        src={getYouTubeEmbedUrl(video.youtubeId, isActive)}
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          cursor: 'pointer'
        }}
        onClick={handleVideoClick}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        title={video.title}
      />

      {/* Video Overlay for Interaction */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        cursor: 'pointer'
      }}
      onClick={handleVideoClick}
      />

      {/* Right Side Actions (TikTok Style) */}
      <Box sx={{
        position: 'absolute',
        right: 12,
        bottom: 100,
        display: 'flex',
        flexDirection: 'column',
        gap: 3,
        zIndex: 3
      }}>
        {/* Profile Avatar */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Avatar
            src={video.author.avatar}
            sx={{ 
              width: 48, 
              height: 48, 
              border: '2px solid white',
              cursor: 'pointer'
            }}
          />
          {video.author.verified && (
            <Box sx={{
              bgcolor: '#1976d2',
              borderRadius: '50%',
              width: 20,
              height: 20,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mt: -1,
              border: '2px solid white'
            }}>
              <Typography sx={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>✓</Typography>
            </Box>
          )}
        </Box>

        {/* Like Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            onClick={handleLike}
            sx={{
              color: isLiked ? '#e91e63' : 'white',
              bgcolor: 'rgba(0, 0, 0, 0.3)',
              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }
            }}
          >
            {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>
            {likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes}
          </Typography>
        </Box>

        {/* Comment Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            onClick={handleComment}
            sx={{
              color: 'white',
              bgcolor: 'rgba(0, 0, 0, 0.3)',
              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }
            }}
          >
            <ChatBubbleOutlineIcon />
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>
            {video.stats.comments}
          </Typography>
        </Box>

        {/* Save Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            onClick={handleSave}
            sx={{
              color: isSaved ? '#ffc107' : 'white',
              bgcolor: 'rgba(0, 0, 0, 0.3)',
              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }
            }}
          >
            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>
            Simpan
          </Typography>
        </Box>

        {/* Share Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            onClick={handleShare}
            sx={{
              color: 'white',
              bgcolor: 'rgba(0, 0, 0, 0.3)',
              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }
            }}
          >
            <ShareIcon />
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>
            {video.stats.shares}
          </Typography>
        </Box>
      </Box>

      {/* Bottom Info (TikTok Style) */}
      <Box sx={{
        position: 'absolute',
        bottom: 20,
        left: 16,
        right: 80,
        zIndex: 3
      }}>
        {/* Upload Date and Duration */}
        <Typography sx={{ 
          color: 'rgba(255, 255, 255, 0.8)', 
          fontSize: 12, 
          mb: 1 
        }}>
          {formatDate(video.uploadDate)} • {video.duration}
        </Typography>

        {/* Title */}
        <Typography sx={{ 
          color: 'white', 
          fontWeight: 'bold', 
          fontSize: 16, 
          mb: 1,
          lineHeight: 1.3
        }}>
          {video.title}
        </Typography>

        {/* Description */}
        <Typography sx={{ 
          color: 'rgba(255, 255, 255, 0.9)', 
          fontSize: 14, 
          mb: 2,
          lineHeight: 1.4,
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden'
        }}>
          {video.description}
        </Typography>

        {/* Tags */}
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {video.tags.map((tag, index) => (
            <Chip
              key={index}
              label={`#${tag}`}
              size="small"
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                fontSize: 11,
                height: 24,
                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' }
              }}
            />
          ))}
        </Box>
      </Box>
    </Box>
  );
}

// Main VideoFeed Component
export default function VideoFeed() {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load videos from admin database
  useEffect(() => {
    const loadVideos = async () => {
      setLoading(true);
      try {
        // Try Express API first
        let response;
        let data;

        try {
          // Try Express API first (if running)
          response = await fetch('http://localhost:8000/api/videos', { signal: AbortSignal.timeout(1000) });
          data = await response.json();
        } catch (expressError) {
          console.log('Express server not available, trying PHP API...');
          // Fallback to PHP API through Laragon (port 80)
          response = await fetch('http://127.0.0.1/react-news/frontend/src/pages/admin/api.php?action=get_videos');
          data = await response.json();
        }

        if (data.success && Array.isArray(data.data)) {
          // Convert database video data to component format
          const dbVideos = data.data
            .filter(video => video.status === 'published') // Only published videos
            .map(video => ({
              id: video.id,
              videoUrl: video.youtube_url,
              youtubeId: video.youtube_id || extractYouTubeId(video.youtube_url),
              title: video.title,
              description: video.description || video.content?.substring(0, 200) + '...',
              tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [video.category?.toLowerCase() || 'video'],
              author: {
                name: 'News Reporter',
                avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),
                verified: true
              },
              stats: {
                likes: parseInt(video.likes) || 0,
                comments: parseInt(video.comments_count) || 0,
                shares: parseInt(video.shares) || 0
              },
              uploadDate: video.created_at || new Date().toISOString(),
              duration: video.duration || '03:00',
              category: video.category || 'Video'
            }));

          // Use database videos if available, otherwise show empty state
          if (dbVideos.length > 0) {
            setVideos(dbVideos);
            console.log('Loaded videos from database:', dbVideos.length);
          } else {
            console.log('No published videos found, showing empty state');
            setVideos([]);
          }
        } else {
          console.log('Failed to load videos from API, showing empty state');
          setVideos([]);
        }
      } catch (error) {
        console.error('Failed to load videos:', error);
        // Show empty state on error
        setVideos([]);
      } finally {
        setLoading(false);
      }
    };

    loadVideos();
  }, []);

  // Extract YouTube video ID from URL
  const extractYouTubeId = (url) => {
    if (!url) return null;
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : 'dQw4w9WgXcQ'; // Default video ID
  };

  // Handle scroll to change videos (only if videos exist)
  useEffect(() => {
    if (videos.length === 0) return; // Don't add scroll listener if no videos

    const handleScroll = (e) => {
      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {
        setCurrentVideoIndex(prev => prev + 1);
      } else if (e.deltaY < 0 && currentVideoIndex > 0) {
        setCurrentVideoIndex(prev => prev - 1);
      }
    };

    window.addEventListener('wheel', handleScroll);
    return () => window.removeEventListener('wheel', handleScroll);
  }, [currentVideoIndex, videos.length]);

  // Function to reload videos
  const reloadVideos = () => {
    setLoading(true);
    // Trigger the useEffect to reload videos
    window.location.reload();
  };

  // Empty state placeholder when no videos are available
  const EmptyVideoState = () => (
    <Box sx={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      bgcolor: '#000',
      color: 'white',
      padding: 3,
      textAlign: 'center'
    }}>
      <Box sx={{
        width: 80,
        height: 80,
        borderRadius: '50%',
        bgcolor: 'rgba(255,255,255,0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        mb: 3
      }}>
        <PlayArrowIcon sx={{ fontSize: 40, opacity: 0.7 }} />
      </Box>

      <Typography variant="h6" sx={{ mb: 1 }}>
        Belum Ada Video
      </Typography>

      <Typography variant="body2" sx={{ opacity: 0.7, maxWidth: 300, mb: 4 }}>
        Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.
      </Typography>

      {/* Refresh Button */}
      <IconButton
        onClick={reloadVideos}
        sx={{
          color: 'white',
          bgcolor: 'rgba(255, 255, 255, 0.1)',
          '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' },
          mb: 3
        }}
      >
        <i className="fas fa-refresh" style={{ fontSize: 20 }} />
      </IconButton>

      <Typography variant="caption" sx={{ opacity: 0.5 }}>
        Ketuk untuk memuat ulang
      </Typography>

      {/* Placeholder UI Elements */}
      <Box sx={{
        display: 'flex',
        gap: 4,
        mt: 2
      }}>
        {/* Like Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            disabled
            sx={{
              color: 'white',
              bgcolor: 'rgba(255, 255, 255, 0.1)',
              '&.Mui-disabled': { opacity: 0.5 }
            }}
          >
            <FavoriteBorderIcon />
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>
            0
          </Typography>
        </Box>

        {/* Comment Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            disabled
            sx={{
              color: 'white',
              bgcolor: 'rgba(255, 255, 255, 0.1)',
              '&.Mui-disabled': { opacity: 0.5 }
            }}
          >
            <ChatBubbleOutlineIcon />
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>
            0
          </Typography>
        </Box>

        {/* Share Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            disabled
            sx={{
              color: 'white',
              bgcolor: 'rgba(255, 255, 255, 0.1)',
              '&.Mui-disabled': { opacity: 0.5 }
            }}
          >
            <ShareIcon />
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>
            0
          </Typography>
        </Box>
      </Box>
    </Box>
  );

  // Loading state
  const LoadingVideoState = () => (
    <Box sx={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      bgcolor: '#000',
      color: 'white'
    }}>
      <Box sx={{
        width: 60,
        height: 60,
        borderRadius: '50%',
        border: '3px solid rgba(255,255,255,0.3)',
        borderTop: '3px solid white',
        animation: 'spin 1s linear infinite',
        mb: 2,
        '@keyframes spin': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' }
        }
      }} />

      <Typography variant="body1">
        Memuat Video...
      </Typography>
    </Box>
  );

  return (
    <Box sx={{
      height: '100vh',
      overflow: 'hidden',
      position: 'relative'
    }}>
      {loading ? (
        <LoadingVideoState />
      ) : videos.length > 0 ? (
        <>
          {videos.map((video, index) => (
            <Box
              key={video.id}
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                transform: `translateY(${(index - currentVideoIndex) * 100}%)`,
                transition: 'transform 0.3s ease-in-out'
              }}
            >
              <VideoItem
                video={video}
                isActive={index === currentVideoIndex}
              />
            </Box>
          ))}

          {/* Video Counter */}
          <Box sx={{
            position: 'absolute',
            top: 20,
            right: 20,
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            borderRadius: 2,
            px: 2,
            py: 1,
            zIndex: 4
          }}>
            <Typography sx={{ color: 'white', fontSize: 12 }}>
              {currentVideoIndex + 1} / {videos.length}
            </Typography>
          </Box>
        </>
      ) : (
        <EmptyVideoState />
      )}
    </Box>
  );
}
