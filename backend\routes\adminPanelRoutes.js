const express = require('express');
const router = express.Router();
const path = require('path');

// Middleware untuk serve static files admin
router.use('/assets', express.static(path.join(__dirname, '../../frontend/src/pages/admin/assets')));
router.use('/pages', express.static(path.join(__dirname, '../../frontend/src/pages/admin/pages')));

// Route untuk admin dashboard
router.get('/dashboard', (req, res) => {
  // Redirect ke file PHP dengan query parameters
  const queryString = req.url.includes('?') ? req.url.split('?')[1] : '';
  const redirectUrl = queryString ? 
    `/admin/php/dashboard.php?${queryString}` : 
    '/admin/php/dashboard.php';
  res.redirect(redirectUrl);
});

// Route untuk file PHP admin
router.get('/php/:file', (req, res) => {
  const filename = req.params.file;
  const allowedFiles = [
    'dashboard.php', 
    'api.php', 
    'config.php',
    'auth_api.php'
  ];
  
  if (allowedFiles.includes(filename)) {
    res.sendFile(path.join(__dirname, '../../frontend/src/pages/admin', filename));
  } else {
    res.status(404).send('File not found');
  }
});

// Route untuk halaman admin dengan parameter
router.get('/dashboard/:page', (req, res) => {
  const page = req.params.page;
  const queryString = req.url.includes('?') ? req.url.split('?')[1] : '';
  const redirectUrl = queryString ? 
    `/admin/php/dashboard.php?page=${page}&${queryString}` : 
    `/admin/php/dashboard.php?page=${page}`;
  res.redirect(redirectUrl);
});

// Route untuk API admin
router.all('/api/:action?', (req, res) => {
  // Proxy ke api.php
  const action = req.params.action || req.query.action || req.body.action;
  const queryString = new URLSearchParams(req.query).toString();
  const redirectUrl = `/admin/php/api.php?action=${action}&${queryString}`;
  res.redirect(307, redirectUrl); // 307 preserves method and body
});

module.exports = router;
