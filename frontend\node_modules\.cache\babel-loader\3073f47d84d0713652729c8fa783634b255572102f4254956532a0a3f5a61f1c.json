{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\components\\\\VideoFeed.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, IconButton, Avatar, Chip, useTheme, useMediaQuery } from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Sample video data with YouTube URLs (in real app, this would come from API)\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst sampleVideos = [{\n  id: 1,\n  videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&mute=1&controls=0&loop=1&playlist=dQw4w9WgXcQ',\n  youtubeId: 'dQw4w9WgXcQ',\n  title: 'Breaking News: Teknologi AI Terbaru',\n  description: 'Perkembangan teknologi AI yang mengubah dunia digital saat ini. Simak ulasan lengkapnya dalam video berita eksklusif ini!',\n  tags: ['teknologi', 'ai', 'digital', 'inovasi', 'berita'],\n  author: {\n    name: 'Tech Reporter',\n    avatar: 'https://i.pravatar.cc/150?img=1',\n    verified: true\n  },\n  stats: {\n    likes: 1250,\n    comments: 89,\n    shares: 45\n  },\n  uploadDate: '2024-01-15T10:30:00Z',\n  duration: '02:45',\n  category: 'Teknologi'\n}, {\n  id: 2,\n  videoUrl: 'https://www.youtube.com/embed/ScMzIvxBSi4?autoplay=1&mute=1&controls=0&loop=1&playlist=ScMzIvxBSi4',\n  youtubeId: 'ScMzIvxBSi4',\n  title: 'Olahraga: Final Sepak Bola Spektakuler',\n  description: 'Highlight pertandingan final yang sangat menegangkan. Gol spektakuler di menit terakhir yang mengubah segalanya!',\n  tags: ['olahraga', 'sepakbola', 'final', 'highlight', 'berita'],\n  author: {\n    name: 'Sports News',\n    avatar: 'https://i.pravatar.cc/150?img=2',\n    verified: true\n  },\n  stats: {\n    likes: 2100,\n    comments: 156,\n    shares: 78\n  },\n  uploadDate: '2024-01-15T14:20:00Z',\n  duration: '03:12',\n  category: 'Olahraga'\n}, {\n  id: 3,\n  videoUrl: 'https://www.youtube.com/embed/jNQXAC9IVRw?autoplay=1&mute=1&controls=0&loop=1&playlist=jNQXAC9IVRw',\n  youtubeId: 'jNQXAC9IVRw',\n  title: 'Kuliner: Resep Masakan Tradisional Nusantara',\n  description: 'Cara membuat masakan tradisional yang lezat dan mudah. Cocok untuk pemula yang ingin belajar memasak!',\n  tags: ['kuliner', 'resep', 'tradisional', 'masakan', 'nusantara'],\n  author: {\n    name: 'Chef Indonesia',\n    avatar: 'https://i.pravatar.cc/150?img=3',\n    verified: false\n  },\n  stats: {\n    likes: 890,\n    comments: 67,\n    shares: 34\n  },\n  uploadDate: '2024-01-15T16:45:00Z',\n  duration: '04:30',\n  category: 'Kuliner'\n}, {\n  id: 4,\n  videoUrl: 'https://www.youtube.com/embed/M7lc1UVf-VE?autoplay=1&mute=1&controls=0&loop=1&playlist=M7lc1UVf-VE',\n  youtubeId: 'M7lc1UVf-VE',\n  title: 'Politik: Update Terkini Pemerintahan',\n  description: 'Berita politik terbaru dan analisis mendalam tentang kebijakan pemerintah yang berdampak pada masyarakat.',\n  tags: ['politik', 'pemerintah', 'kebijakan', 'berita', 'analisis'],\n  author: {\n    name: 'Political Analyst',\n    avatar: 'https://i.pravatar.cc/150?img=4',\n    verified: true\n  },\n  stats: {\n    likes: 1580,\n    comments: 234,\n    shares: 89\n  },\n  uploadDate: '2024-01-15T18:30:00Z',\n  duration: '05:15',\n  category: 'Politik'\n}];\n\n// Individual Video Item Component\nfunction VideoItem({\n  video,\n  isActive,\n  onVideoClick\n}) {\n  _s();\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // TikTok aspect ratio: 9:16 (vertical)\n  const videoHeight = isMobile ? '100vh' : '80vh';\n  const videoWidth = isMobile ? '100vw' : '45vh'; // 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '0',\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '0',\n      disablekb: '1'\n    });\n    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n  };\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n    try {\n      // Call the API to increment view count\n      await fetch(`http://localhost:8000/api/videos/${video.id}/view`, {\n        method: 'POST'\n      });\n    } catch (error) {\n      console.error('Failed to update view count:', error);\n    }\n  };\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n    try {\n      // Use the new Express API endpoint\n      const response = await fetch(`http://localhost:8000/api/videos/${video.id}/like`, {\n        method: 'POST'\n      });\n      const data = await response.json();\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked);\n        setLikes(data.likes);\n      } else {\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n  const handleShare = async () => {\n    console.log('Share video:', video.id);\n    try {\n      // Call the API to increment share count\n      const response = await fetch(`http://localhost:8000/api/videos/${video.id}/share`, {\n        method: 'POST'\n      });\n      const data = await response.json();\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares;\n      }\n    } catch (error) {\n      console.error('Failed to update share count:', error);\n    }\n  };\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"iframe\", {\n      ref: iframeRef,\n      src: getYouTubeEmbedUrl(video.youtubeId, isActive),\n      style: {\n        width: '100%',\n        height: '100%',\n        border: 'none',\n        cursor: 'pointer'\n      },\n      onClick: handleVideoClick,\n      allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n      allowFullScreen: true,\n      title: video.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      },\n      onClick: handleVideoClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: video.author.avatar,\n          sx: {\n            width: 48,\n            height: 48,\n            border: '2px solid white',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), video.author.verified && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            bgcolor: '#1976d2',\n            borderRadius: '50%',\n            width: 20,\n            height: 20,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mt: -1,\n            border: '2px solid white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: 10,\n              fontWeight: 'bold'\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleLike,\n          sx: {\n            color: isLiked ? '#e91e63' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isLiked ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleComment,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.comments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSave,\n          sx: {\n            color: isSaved ? '#ffc107' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isSaved ? /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(BookmarkBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: \"Simpan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleShare,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.shares\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.8)',\n          fontSize: 12,\n          mb: 1\n        },\n        children: [formatDate(video.uploadDate), \" \\u2022 \", video.duration]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'white',\n          fontWeight: 'bold',\n          fontSize: 16,\n          mb: 1,\n          lineHeight: 1.3\n        },\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: 14,\n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        },\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: video.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(Chip, {\n          label: `#${tag}`,\n          size: \"small\",\n          sx: {\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            fontSize: 11,\n            height: 24,\n            '&:hover': {\n              bgcolor: 'rgba(255, 255, 255, 0.3)'\n            }\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n}\n\n// Main VideoFeed Component\n_s(VideoItem, \"YfQkD9vK244jX6up3O1KMuen6o4=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = VideoItem;\nexport default function VideoFeed() {\n  _s2();\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState(sampleVideos);\n  const [loading, setLoading] = useState(false);\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        // Use the new Express API endpoint\n        const response = await fetch('http://localhost:8000/api/videos');\n        const data = await response.json();\n        if (data.success && Array.isArray(data.data)) {\n          // Convert database video data to component format\n          const dbVideos = data.data.filter(video => video.status === 'published') // Only published videos\n          .map(video => {\n            var _video$content, _video$category;\n            return {\n              id: video.id,\n              videoUrl: video.youtube_url,\n              youtubeId: video.youtube_id || extractYouTubeId(video.youtube_url),\n              title: video.title,\n              description: video.description || ((_video$content = video.content) === null || _video$content === void 0 ? void 0 : _video$content.substring(0, 200)) + '...',\n              tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [((_video$category = video.category) === null || _video$category === void 0 ? void 0 : _video$category.toLowerCase()) || 'video'],\n              author: {\n                name: 'News Reporter',\n                avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                verified: true\n              },\n              stats: {\n                likes: parseInt(video.likes) || 0,\n                comments: parseInt(video.comments_count) || 0,\n                shares: parseInt(video.shares) || 0\n              },\n              uploadDate: video.created_at || new Date().toISOString(),\n              duration: video.duration || '03:00',\n              category: video.category || 'Video'\n            };\n          });\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            setVideos(dbVideos);\n            console.log('Loaded videos from database:', dbVideos.length);\n          } else {\n            console.log('No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.log('Failed to load videos from API, showing empty state');\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadVideos();\n  }, []);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = url => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return match && match[2].length === 11 ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Handle scroll to change videos (simplified for demo)\n  useEffect(() => {\n    const handleScroll = e => {\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n    };\n    window.addEventListener('wheel', handleScroll);\n    return () => window.removeEventListener('wheel', handleScroll);\n  }, [currentVideoIndex, videos.length]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative'\n    },\n    children: [videos.map((video, index) => /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n        transition: 'transform 0.3s ease-in-out'\n      },\n      children: /*#__PURE__*/_jsxDEV(VideoItem, {\n        video: video,\n        isActive: index === currentVideoIndex\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 11\n      }, this)\n    }, video.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 20,\n        right: 20,\n        bgcolor: 'rgba(0, 0, 0, 0.5)',\n        borderRadius: 2,\n        px: 2,\n        py: 1,\n        zIndex: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'white',\n          fontSize: 12\n        },\n        children: [currentVideoIndex + 1, \" / \", videos.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 525,\n    columnNumber: 5\n  }, this);\n}\n_s2(VideoFeed, \"8BmWOwebzm4bLiBmKZe+4iq4OL4=\");\n_c2 = VideoFeed;\nvar _c, _c2;\n$RefreshReg$(_c, \"VideoItem\");\n$RefreshReg$(_c2, \"VideoFeed\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "IconButton", "Avatar", "Chip", "useTheme", "useMediaQuery", "FavoriteIcon", "FavoriteBorderIcon", "ChatBubbleOutlineIcon", "BookmarkBorderIcon", "BookmarkIcon", "ShareIcon", "PlayArrowIcon", "PauseIcon", "jsxDEV", "_jsxDEV", "sampleVideos", "id", "videoUrl", "youtubeId", "title", "description", "tags", "author", "name", "avatar", "verified", "stats", "likes", "comments", "shares", "uploadDate", "duration", "category", "VideoItem", "video", "isActive", "onVideoClick", "_s", "isLiked", "setIsLiked", "isSaved", "setIsSaved", "setLikes", "iframeRef", "theme", "isMobile", "breakpoints", "down", "videoHeight", "videoWidth", "getYouTubeEmbedUrl", "videoId", "autoplay", "params", "URLSearchParams", "mute", "controls", "loop", "playlist", "rel", "showinfo", "modestbranding", "iv_load_policy", "fs", "disablekb", "toString", "handleVideoClick", "console", "log", "fetch", "method", "error", "handleLike", "wasLiked", "prev", "response", "data", "json", "success", "liked", "handleSave", "handleShare", "handleComment", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "sx", "position", "height", "width", "max<PERSON><PERSON><PERSON>", "mx", "bgcolor", "display", "alignItems", "justifyContent", "overflow", "children", "ref", "src", "style", "border", "cursor", "onClick", "allow", "allowFullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "left", "right", "bottom", "zIndex", "flexDirection", "gap", "borderRadius", "mt", "color", "fontSize", "fontWeight", "toFixed", "mb", "lineHeight", "WebkitLineClamp", "WebkitBoxOrient", "flexWrap", "map", "tag", "index", "label", "size", "_c", "VideoFeed", "_s2", "currentVideoIndex", "setCurrentVideoIndex", "videos", "setVideos", "loading", "setLoading", "loadVideos", "Array", "isArray", "dbVideos", "filter", "status", "_video$content", "_video$category", "youtube_url", "youtube_id", "extractYouTubeId", "content", "substring", "split", "trim", "toLowerCase", "parseInt", "comments_count", "created_at", "toISOString", "length", "url", "regExp", "match", "handleScroll", "e", "deltaY", "window", "addEventListener", "removeEventListener", "transform", "transition", "px", "py", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/components/VideoFeed.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Avatar,\n  Chip,\n  useTheme,\n  useMediaQuery\n} from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Sample video data with YouTube URLs (in real app, this would come from API)\nconst sampleVideos = [\n  {\n    id: 1,\n    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&mute=1&controls=0&loop=1&playlist=dQw4w9WgXcQ',\n    youtubeId: 'dQw4w9WgXcQ',\n    title: 'Breaking News: Teknologi AI Terbaru',\n    description: 'Perkembangan teknologi AI yang mengubah dunia digital saat ini. Simak ulasan lengkapnya dalam video berita eksklusif ini!',\n    tags: ['teknologi', 'ai', 'digital', 'inovasi', 'berita'],\n    author: {\n      name: 'Tech Reporter',\n      avatar: 'https://i.pravatar.cc/150?img=1',\n      verified: true\n    },\n    stats: {\n      likes: 1250,\n      comments: 89,\n      shares: 45\n    },\n    uploadDate: '2024-01-15T10:30:00Z',\n    duration: '02:45',\n    category: 'Teknologi'\n  },\n  {\n    id: 2,\n    videoUrl: 'https://www.youtube.com/embed/ScMzIvxBSi4?autoplay=1&mute=1&controls=0&loop=1&playlist=ScMzIvxBSi4',\n    youtubeId: 'ScMzIvxBSi4',\n    title: 'Olahraga: Final Sepak Bola Spektakuler',\n    description: 'Highlight pertandingan final yang sangat menegangkan. Gol spektakuler di menit terakhir yang mengubah segalanya!',\n    tags: ['olahraga', 'sepakbola', 'final', 'highlight', 'berita'],\n    author: {\n      name: 'Sports News',\n      avatar: 'https://i.pravatar.cc/150?img=2',\n      verified: true\n    },\n    stats: {\n      likes: 2100,\n      comments: 156,\n      shares: 78\n    },\n    uploadDate: '2024-01-15T14:20:00Z',\n    duration: '03:12',\n    category: 'Olahraga'\n  },\n  {\n    id: 3,\n    videoUrl: 'https://www.youtube.com/embed/jNQXAC9IVRw?autoplay=1&mute=1&controls=0&loop=1&playlist=jNQXAC9IVRw',\n    youtubeId: 'jNQXAC9IVRw',\n    title: 'Kuliner: Resep Masakan Tradisional Nusantara',\n    description: 'Cara membuat masakan tradisional yang lezat dan mudah. Cocok untuk pemula yang ingin belajar memasak!',\n    tags: ['kuliner', 'resep', 'tradisional', 'masakan', 'nusantara'],\n    author: {\n      name: 'Chef Indonesia',\n      avatar: 'https://i.pravatar.cc/150?img=3',\n      verified: false\n    },\n    stats: {\n      likes: 890,\n      comments: 67,\n      shares: 34\n    },\n    uploadDate: '2024-01-15T16:45:00Z',\n    duration: '04:30',\n    category: 'Kuliner'\n  },\n  {\n    id: 4,\n    videoUrl: 'https://www.youtube.com/embed/M7lc1UVf-VE?autoplay=1&mute=1&controls=0&loop=1&playlist=M7lc1UVf-VE',\n    youtubeId: 'M7lc1UVf-VE',\n    title: 'Politik: Update Terkini Pemerintahan',\n    description: 'Berita politik terbaru dan analisis mendalam tentang kebijakan pemerintah yang berdampak pada masyarakat.',\n    tags: ['politik', 'pemerintah', 'kebijakan', 'berita', 'analisis'],\n    author: {\n      name: 'Political Analyst',\n      avatar: 'https://i.pravatar.cc/150?img=4',\n      verified: true\n    },\n    stats: {\n      likes: 1580,\n      comments: 234,\n      shares: 89\n    },\n    uploadDate: '2024-01-15T18:30:00Z',\n    duration: '05:15',\n    category: 'Politik'\n  }\n];\n\n// Individual Video Item Component\nfunction VideoItem({ video, isActive, onVideoClick }) {\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // TikTok aspect ratio: 9:16 (vertical)\n  const videoHeight = isMobile ? '100vh' : '80vh';\n  const videoWidth = isMobile ? '100vw' : '45vh'; // 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '0',\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '0',\n      disablekb: '1'\n    });\n    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n  };\n\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n\n    try {\n      // Call the API to increment view count\n      await fetch(`http://localhost:8000/api/videos/${video.id}/view`, {\n        method: 'POST'\n      });\n    } catch (error) {\n      console.error('Failed to update view count:', error);\n    }\n  };\n\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n\n    try {\n      // Use the new Express API endpoint\n      const response = await fetch(`http://localhost:8000/api/videos/${video.id}/like`, {\n        method: 'POST'\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked);\n        setLikes(data.likes);\n      } else {\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n\n  const handleShare = async () => {\n    console.log('Share video:', video.id);\n\n    try {\n      // Call the API to increment share count\n      const response = await fetch(`http://localhost:8000/api/videos/${video.id}/share`, {\n        method: 'POST'\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares;\n      }\n    } catch (error) {\n      console.error('Failed to update share count:', error);\n    }\n  };\n\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n\n  return (\n    <Box sx={{\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden'\n    }}>\n      {/* YouTube Video Iframe */}\n      <iframe\n        ref={iframeRef}\n        src={getYouTubeEmbedUrl(video.youtubeId, isActive)}\n        style={{\n          width: '100%',\n          height: '100%',\n          border: 'none',\n          cursor: 'pointer'\n        }}\n        onClick={handleVideoClick}\n        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n        allowFullScreen\n        title={video.title}\n      />\n\n      {/* Video Overlay for Interaction */}\n      <Box sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      }}\n      onClick={handleVideoClick}\n      />\n\n      {/* Right Side Actions (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      }}>\n        {/* Profile Avatar */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <Avatar\n            src={video.author.avatar}\n            sx={{ \n              width: 48, \n              height: 48, \n              border: '2px solid white',\n              cursor: 'pointer'\n            }}\n          />\n          {video.author.verified && (\n            <Box sx={{\n              bgcolor: '#1976d2',\n              borderRadius: '50%',\n              width: 20,\n              height: 20,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mt: -1,\n              border: '2px solid white'\n            }}>\n              <Typography sx={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>✓</Typography>\n            </Box>\n          )}\n        </Box>\n\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleLike}\n            sx={{\n              color: isLiked ? '#e91e63' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes}\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleComment}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.comments}\n          </Typography>\n        </Box>\n\n        {/* Save Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleSave}\n            sx={{\n              color: isSaved ? '#ffc107' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            Simpan\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleShare}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.shares}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Bottom Info (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      }}>\n        {/* Upload Date and Duration */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.8)', \n          fontSize: 12, \n          mb: 1 \n        }}>\n          {formatDate(video.uploadDate)} • {video.duration}\n        </Typography>\n\n        {/* Title */}\n        <Typography sx={{ \n          color: 'white', \n          fontWeight: 'bold', \n          fontSize: 16, \n          mb: 1,\n          lineHeight: 1.3\n        }}>\n          {video.title}\n        </Typography>\n\n        {/* Description */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.9)', \n          fontSize: 14, \n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        }}>\n          {video.description}\n        </Typography>\n\n        {/* Tags */}\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          {video.tags.map((tag, index) => (\n            <Chip\n              key={index}\n              label={`#${tag}`}\n              size=\"small\"\n              sx={{\n                bgcolor: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                fontSize: 11,\n                height: 24,\n                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' }\n              }}\n            />\n          ))}\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\n// Main VideoFeed Component\nexport default function VideoFeed() {\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState(sampleVideos);\n  const [loading, setLoading] = useState(false);\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        // Use the new Express API endpoint\n        const response = await fetch('http://localhost:8000/api/videos');\n        const data = await response.json();\n\n        if (data.success && Array.isArray(data.data)) {\n          // Convert database video data to component format\n          const dbVideos = data.data\n            .filter(video => video.status === 'published') // Only published videos\n            .map(video => ({\n              id: video.id,\n              videoUrl: video.youtube_url,\n              youtubeId: video.youtube_id || extractYouTubeId(video.youtube_url),\n              title: video.title,\n              description: video.description || video.content?.substring(0, 200) + '...',\n              tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [video.category?.toLowerCase() || 'video'],\n              author: {\n                name: 'News Reporter',\n                avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                verified: true\n              },\n              stats: {\n                likes: parseInt(video.likes) || 0,\n                comments: parseInt(video.comments_count) || 0,\n                shares: parseInt(video.shares) || 0\n              },\n              uploadDate: video.created_at || new Date().toISOString(),\n              duration: video.duration || '03:00',\n              category: video.category || 'Video'\n            }));\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            setVideos(dbVideos);\n            console.log('Loaded videos from database:', dbVideos.length);\n          } else {\n            console.log('No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.log('Failed to load videos from API, showing empty state');\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadVideos();\n  }, []);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = (url) => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return (match && match[2].length === 11) ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Handle scroll to change videos (simplified for demo)\n  useEffect(() => {\n    const handleScroll = (e) => {\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n    };\n\n    window.addEventListener('wheel', handleScroll);\n    return () => window.removeEventListener('wheel', handleScroll);\n  }, [currentVideoIndex, videos.length]);\n\n  return (\n    <Box sx={{\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative'\n    }}>\n      {videos.map((video, index) => (\n        <Box\n          key={video.id}\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n            transition: 'transform 0.3s ease-in-out'\n          }}\n        >\n          <VideoItem\n            video={video}\n            isActive={index === currentVideoIndex}\n          />\n        </Box>\n      ))}\n\n      {/* Video Counter */}\n      <Box sx={{\n        position: 'absolute',\n        top: 20,\n        right: 20,\n        bgcolor: 'rgba(0, 0, 0, 0.5)',\n        borderRadius: 2,\n        px: 2,\n        py: 1,\n        zIndex: 4\n      }}>\n        <Typography sx={{ color: 'white', fontSize: 12 }}>\n          {currentVideoIndex + 1} / {videos.length}\n        </Typography>\n      </Box>\n    </Box>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,SAAS,MAAM,2BAA2B;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG,CACnB;EACEC,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,oGAAoG;EAC9GC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,qCAAqC;EAC5CC,WAAW,EAAE,2HAA2H;EACxIC,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;EACzDC,MAAM,EAAE;IACNC,IAAI,EAAE,eAAe;IACrBC,MAAM,EAAE,iCAAiC;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE,sBAAsB;EAClCC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,oGAAoG;EAC9GC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,wCAAwC;EAC/CC,WAAW,EAAE,kHAAkH;EAC/HC,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC;EAC/DC,MAAM,EAAE;IACNC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE,iCAAiC;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE,sBAAsB;EAClCC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,oGAAoG;EAC9GC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,8CAA8C;EACrDC,WAAW,EAAE,uGAAuG;EACpHC,IAAI,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,CAAC;EACjEC,MAAM,EAAE;IACNC,IAAI,EAAE,gBAAgB;IACtBC,MAAM,EAAE,iCAAiC;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE,sBAAsB;EAClCC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,oGAAoG;EAC9GC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,sCAAsC;EAC7CC,WAAW,EAAE,2GAA2G;EACxHC,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC;EAClEC,MAAM,EAAE;IACNC,IAAI,EAAE,mBAAmB;IACzBC,MAAM,EAAE,iCAAiC;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE,sBAAsB;EAClCC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACZ,CAAC,CACF;;AAED;AACA,SAASC,SAASA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,KAAK,EAAEe,QAAQ,CAAC,GAAG/C,QAAQ,CAACuC,KAAK,CAACR,KAAK,CAACC,KAAK,CAAC;EACrD,MAAMgB,SAAS,GAAG/C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMgD,KAAK,GAAGzC,QAAQ,CAAC,CAAC;EACxB,MAAM0C,QAAQ,GAAGzC,aAAa,CAACwC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAMC,WAAW,GAAGH,QAAQ,GAAG,OAAO,GAAG,MAAM;EAC/C,MAAMI,UAAU,GAAGJ,QAAQ,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC;;EAEhD;EACA,MAAMK,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,GAAG,KAAK,KAAK;IACxD,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCF,QAAQ,EAAEA,QAAQ,GAAG,GAAG,GAAG,GAAG;MAC9BG,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAEP,OAAO;MACjBQ,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,GAAG;MACbC,cAAc,EAAE,GAAG;MACnBC,cAAc,EAAE,GAAG;MACnBC,EAAE,EAAE,GAAG;MACPC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,OAAO,iCAAiCb,OAAO,IAAIE,MAAM,CAACY,QAAQ,CAAC,CAAC,EAAE;EACxE,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC;IACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAElC,KAAK,CAACf,KAAK,CAAC;IAE1C,IAAI;MACF;MACA,MAAMkD,KAAK,CAAC,oCAAoCnC,KAAK,CAAClB,EAAE,OAAO,EAAE;QAC/DsD,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,QAAQ,GAAGnC,OAAO;;IAExB;IACAC,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBI,QAAQ,CAACgC,IAAI,IAAIpC,OAAO,GAAGoC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAE/C,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAMN,KAAK,CAAC,oCAAoCnC,KAAK,CAAClB,EAAE,OAAO,EAAE;QAChFsD,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,MAAMM,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB;QACAvC,UAAU,CAACqC,IAAI,CAACG,KAAK,CAAC;QACtBrC,QAAQ,CAACkC,IAAI,CAACjD,KAAK,CAAC;MACtB,CAAC,MAAM;QACL;QACAY,UAAU,CAACkC,QAAQ,CAAC;QACpB/B,QAAQ,CAACgC,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;MAClD;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACAhC,UAAU,CAACkC,QAAQ,CAAC;MACpB/B,QAAQ,CAACgC,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvBvC,UAAU,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,MAAMyC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9Bd,OAAO,CAACC,GAAG,CAAC,cAAc,EAAElC,KAAK,CAAClB,EAAE,CAAC;IAErC,IAAI;MACF;MACA,MAAM2D,QAAQ,GAAG,MAAMN,KAAK,CAAC,oCAAoCnC,KAAK,CAAClB,EAAE,QAAQ,EAAE;QACjFsD,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,MAAMM,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB;QACA5C,KAAK,CAACR,KAAK,CAACG,MAAM,GAAG+C,IAAI,CAAC/C,MAAM;MAClC;IACF,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMW,aAAa,GAAGA,CAAA,KAAM;IAC1Bf,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAElC,KAAK,CAAClB,EAAE,CAAC;EACnD,CAAC;EAED,MAAMmE,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE,OAAO,aAAa;IACxC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,cAAc;IAClE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,EAAE,CAAC,aAAa;EACjD,CAAC;EAED,oBACE7E,OAAA,CAAChB,GAAG;IAAC+F,EAAE,EAAE;MACPC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE/C,WAAW;MACnBgD,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAEhD,UAAU;MACpBiD,EAAE,EAAE,MAAM;MACVC,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEA1F,OAAA;MACE2F,GAAG,EAAE9D,SAAU;MACf+D,GAAG,EAAExD,kBAAkB,CAAChB,KAAK,CAAChB,SAAS,EAAEiB,QAAQ,CAAE;MACnDwE,KAAK,EAAE;QACLX,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACda,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE;MACV,CAAE;MACFC,OAAO,EAAE5C,gBAAiB;MAC1B6C,KAAK,EAAC,0FAA0F;MAChGC,eAAe;MACf7F,KAAK,EAAEe,KAAK,CAACf;IAAM;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAGFtG,OAAA,CAAChB,GAAG;MAAC+F,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBuB,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACTZ,MAAM,EAAE;MACV,CAAE;MACFC,OAAO,EAAE5C;IAAiB;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGFtG,OAAA,CAAChB,GAAG;MAAC+F,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpByB,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,GAAG;QACXpB,OAAO,EAAE,MAAM;QACfsB,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,CAAC;QACNF,MAAM,EAAE;MACV,CAAE;MAAAjB,QAAA,gBAEA1F,OAAA,CAAChB,GAAG;QAAC+F,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEsB,aAAa,EAAE,QAAQ;UAAErB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1E1F,OAAA,CAACb,MAAM;UACLyG,GAAG,EAAExE,KAAK,CAACZ,MAAM,CAACE,MAAO;UACzBqE,EAAE,EAAE;YACFG,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVa,MAAM,EAAE,iBAAiB;YACzBC,MAAM,EAAE;UACV;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACDlF,KAAK,CAACZ,MAAM,CAACG,QAAQ,iBACpBX,OAAA,CAAChB,GAAG;UAAC+F,EAAE,EAAE;YACPM,OAAO,EAAE,SAAS;YAClByB,YAAY,EAAE,KAAK;YACnB5B,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVK,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBuB,EAAE,EAAE,CAAC,CAAC;YACNjB,MAAM,EAAE;UACV,CAAE;UAAAJ,QAAA,eACA1F,OAAA,CAACf,UAAU;YAAC8F,EAAE,EAAE;cAAEiC,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,EAAE;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAxB,QAAA,EAAC;UAAC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtG,OAAA,CAAChB,GAAG;QAAC+F,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEsB,aAAa,EAAE,QAAQ;UAAErB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1E1F,OAAA,CAACd,UAAU;UACT8G,OAAO,EAAEtC,UAAW;UACpBqB,EAAE,EAAE;YACFiC,KAAK,EAAExF,OAAO,GAAG,SAAS,GAAG,OAAO;YACpC6D,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,EAEDlE,OAAO,gBAAGxB,OAAA,CAACT,YAAY;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGtG,OAAA,CAACR,kBAAkB;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbtG,OAAA,CAACf,UAAU;UAAC8F,EAAE,EAAE;YAAEiC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAArB,QAAA,EACvD7E,KAAK,GAAG,IAAI,GAAG,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEsG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAGtG;QAAK;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNtG,OAAA,CAAChB,GAAG;QAAC+F,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEsB,aAAa,EAAE,QAAQ;UAAErB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1E1F,OAAA,CAACd,UAAU;UACT8G,OAAO,EAAE5B,aAAc;UACvBW,EAAE,EAAE;YACFiC,KAAK,EAAE,OAAO;YACd3B,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,eAEF1F,OAAA,CAACP,qBAAqB;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbtG,OAAA,CAACf,UAAU;UAAC8F,EAAE,EAAE;YAAEiC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAArB,QAAA,EACvDtE,KAAK,CAACR,KAAK,CAACE;QAAQ;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNtG,OAAA,CAAChB,GAAG;QAAC+F,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEsB,aAAa,EAAE,QAAQ;UAAErB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1E1F,OAAA,CAACd,UAAU;UACT8G,OAAO,EAAE9B,UAAW;UACpBa,EAAE,EAAE;YACFiC,KAAK,EAAEtF,OAAO,GAAG,SAAS,GAAG,OAAO;YACpC2D,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,EAEDhE,OAAO,gBAAG1B,OAAA,CAACL,YAAY;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGtG,OAAA,CAACN,kBAAkB;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbtG,OAAA,CAACf,UAAU;UAAC8F,EAAE,EAAE;YAAEiC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAArB,QAAA,EAAC;QAE3D;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNtG,OAAA,CAAChB,GAAG;QAAC+F,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEsB,aAAa,EAAE,QAAQ;UAAErB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1E1F,OAAA,CAACd,UAAU;UACT8G,OAAO,EAAE7B,WAAY;UACrBY,EAAE,EAAE;YACFiC,KAAK,EAAE,OAAO;YACd3B,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,eAEF1F,OAAA,CAACJ,SAAS;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbtG,OAAA,CAACf,UAAU;UAAC8F,EAAE,EAAE;YAAEiC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAArB,QAAA,EACvDtE,KAAK,CAACR,KAAK,CAACG;QAAM;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtG,OAAA,CAAChB,GAAG;MAAC+F,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB0B,MAAM,EAAE,EAAE;QACVF,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTE,MAAM,EAAE;MACV,CAAE;MAAAjB,QAAA,gBAEA1F,OAAA,CAACf,UAAU;QAAC8F,EAAE,EAAE;UACdiC,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZG,EAAE,EAAE;QACN,CAAE;QAAA1B,QAAA,GACCrB,UAAU,CAACjD,KAAK,CAACJ,UAAU,CAAC,EAAC,UAAG,EAACI,KAAK,CAACH,QAAQ;MAAA;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGbtG,OAAA,CAACf,UAAU;QAAC8F,EAAE,EAAE;UACdiC,KAAK,EAAE,OAAO;UACdE,UAAU,EAAE,MAAM;UAClBD,QAAQ,EAAE,EAAE;UACZG,EAAE,EAAE,CAAC;UACLC,UAAU,EAAE;QACd,CAAE;QAAA3B,QAAA,EACCtE,KAAK,CAACf;MAAK;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGbtG,OAAA,CAACf,UAAU;QAAC8F,EAAE,EAAE;UACdiC,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZG,EAAE,EAAE,CAAC;UACLC,UAAU,EAAE,GAAG;UACf/B,OAAO,EAAE,aAAa;UACtBgC,eAAe,EAAE,CAAC;UAClBC,eAAe,EAAE,UAAU;UAC3B9B,QAAQ,EAAE;QACZ,CAAE;QAAAC,QAAA,EACCtE,KAAK,CAACd;MAAW;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGbtG,OAAA,CAAChB,GAAG;QAAC+F,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEuB,GAAG,EAAE,CAAC;UAAEW,QAAQ,EAAE;QAAO,CAAE;QAAA9B,QAAA,EACpDtE,KAAK,CAACb,IAAI,CAACkH,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACzB3H,OAAA,CAACZ,IAAI;UAEHwI,KAAK,EAAE,IAAIF,GAAG,EAAG;UACjBG,IAAI,EAAC,OAAO;UACZ9C,EAAE,EAAE;YACFM,OAAO,EAAE,0BAA0B;YACnC2B,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,EAAE;YACZhC,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;cAAEI,OAAO,EAAE;YAA2B;UACnD;QAAE,GATGsC,KAAK;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAA/E,EAAA,CAxUSJ,SAAS;EAAA,QAKF9B,QAAQ,EACLC,aAAa;AAAA;AAAAwI,EAAA,GANvB3G,SAAS;AAyUlB,eAAe,SAAS4G,SAASA,CAAA,EAAG;EAAAC,GAAA;EAClC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrJ,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACsJ,MAAM,EAAEC,SAAS,CAAC,GAAGvJ,QAAQ,CAACoB,YAAY,CAAC;EAClD,MAAM,CAACoI,OAAO,EAAEC,UAAU,CAAC,GAAGzJ,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACAE,SAAS,CAAC,MAAM;IACd,MAAMwJ,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7BD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAMzE,QAAQ,GAAG,MAAMN,KAAK,CAAC,kCAAkC,CAAC;QAChE,MAAMO,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,IAAIwE,KAAK,CAACC,OAAO,CAAC3E,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5C;UACA,MAAM4E,QAAQ,GAAG5E,IAAI,CAACA,IAAI,CACvB6E,MAAM,CAACvH,KAAK,IAAIA,KAAK,CAACwH,MAAM,KAAK,WAAW,CAAC,CAAC;UAAA,CAC9CnB,GAAG,CAACrG,KAAK;YAAA,IAAAyH,cAAA,EAAAC,eAAA;YAAA,OAAK;cACb5I,EAAE,EAAEkB,KAAK,CAAClB,EAAE;cACZC,QAAQ,EAAEiB,KAAK,CAAC2H,WAAW;cAC3B3I,SAAS,EAAEgB,KAAK,CAAC4H,UAAU,IAAIC,gBAAgB,CAAC7H,KAAK,CAAC2H,WAAW,CAAC;cAClE1I,KAAK,EAAEe,KAAK,CAACf,KAAK;cAClBC,WAAW,EAAEc,KAAK,CAACd,WAAW,IAAI,EAAAuI,cAAA,GAAAzH,KAAK,CAAC8H,OAAO,cAAAL,cAAA,uBAAbA,cAAA,CAAeM,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAG,KAAK;cAC1E5I,IAAI,EAAEa,KAAK,CAACb,IAAI,GAAGa,KAAK,CAACb,IAAI,CAAC6I,KAAK,CAAC,GAAG,CAAC,CAAC3B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAAC2B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAAP,eAAA,GAAA1H,KAAK,CAACF,QAAQ,cAAA4H,eAAA,uBAAdA,eAAA,CAAgBQ,WAAW,CAAC,CAAC,KAAI,OAAO,CAAC;cAC5G9I,MAAM,EAAE;gBACNC,IAAI,EAAE,eAAe;gBACrBC,MAAM,EAAE,gCAAgC,IAAIU,KAAK,CAAClB,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC9DS,QAAQ,EAAE;cACZ,CAAC;cACDC,KAAK,EAAE;gBACLC,KAAK,EAAE0I,QAAQ,CAACnI,KAAK,CAACP,KAAK,CAAC,IAAI,CAAC;gBACjCC,QAAQ,EAAEyI,QAAQ,CAACnI,KAAK,CAACoI,cAAc,CAAC,IAAI,CAAC;gBAC7CzI,MAAM,EAAEwI,QAAQ,CAACnI,KAAK,CAACL,MAAM,CAAC,IAAI;cACpC,CAAC;cACDC,UAAU,EAAEI,KAAK,CAACqI,UAAU,IAAI,IAAIjF,IAAI,CAAC,CAAC,CAACkF,WAAW,CAAC,CAAC;cACxDzI,QAAQ,EAAEG,KAAK,CAACH,QAAQ,IAAI,OAAO;cACnCC,QAAQ,EAAEE,KAAK,CAACF,QAAQ,IAAI;YAC9B,CAAC;UAAA,CAAC,CAAC;;UAEL;UACA,IAAIwH,QAAQ,CAACiB,MAAM,GAAG,CAAC,EAAE;YACvBvB,SAAS,CAACM,QAAQ,CAAC;YACnBrF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEoF,QAAQ,CAACiB,MAAM,CAAC;UAC9D,CAAC,MAAM;YACLtG,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAC7D8E,SAAS,CAAC,EAAE,CAAC;UACf;QACF,CAAC,MAAM;UACL/E,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE8E,SAAS,CAAC,EAAE,CAAC;QACf;MACF,CAAC,CAAC,OAAO3E,KAAK,EAAE;QACdJ,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA2E,SAAS,CAAC,EAAE,CAAC;MACf,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,gBAAgB,GAAIW,GAAG,IAAK;IAChC,IAAI,CAACA,GAAG,EAAE,OAAO,IAAI;IACrB,MAAMC,MAAM,GAAG,8DAA8D;IAC7E,MAAMC,KAAK,GAAGF,GAAG,CAACE,KAAK,CAACD,MAAM,CAAC;IAC/B,OAAQC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACH,MAAM,KAAK,EAAE,GAAIG,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;EACvE,CAAC;;EAED;EACA/K,SAAS,CAAC,MAAM;IACd,MAAMgL,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIhC,iBAAiB,GAAGE,MAAM,CAACwB,MAAM,GAAG,CAAC,EAAE;QACzDzB,oBAAoB,CAACtE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIoG,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIhC,iBAAiB,GAAG,CAAC,EAAE;QAChDC,oBAAoB,CAACtE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC;IACF,CAAC;IAEDsG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEJ,YAAY,CAAC;IAC9C,OAAO,MAAMG,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEL,YAAY,CAAC;EAChE,CAAC,EAAE,CAAC9B,iBAAiB,EAAEE,MAAM,CAACwB,MAAM,CAAC,CAAC;EAEtC,oBACE3J,OAAA,CAAChB,GAAG;IAAC+F,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfQ,QAAQ,EAAE,QAAQ;MAClBT,QAAQ,EAAE;IACZ,CAAE;IAAAU,QAAA,GACCyC,MAAM,CAACV,GAAG,CAAC,CAACrG,KAAK,EAAEuG,KAAK,kBACvB3H,OAAA,CAAChB,GAAG;MAEF+F,EAAE,EAAE;QACFC,QAAQ,EAAE,UAAU;QACpBuB,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPtB,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACdoF,SAAS,EAAE,cAAc,CAAC1C,KAAK,GAAGM,iBAAiB,IAAI,GAAG,IAAI;QAC9DqC,UAAU,EAAE;MACd,CAAE;MAAA5E,QAAA,eAEF1F,OAAA,CAACmB,SAAS;QACRC,KAAK,EAAEA,KAAM;QACbC,QAAQ,EAAEsG,KAAK,KAAKM;MAAkB;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IAAC,GAdGlF,KAAK,CAAClB,EAAE;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeV,CACN,CAAC,eAGFtG,OAAA,CAAChB,GAAG;MAAC+F,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBuB,GAAG,EAAE,EAAE;QACPE,KAAK,EAAE,EAAE;QACTpB,OAAO,EAAE,oBAAoB;QAC7ByB,YAAY,EAAE,CAAC;QACfyD,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACL7D,MAAM,EAAE;MACV,CAAE;MAAAjB,QAAA,eACA1F,OAAA,CAACf,UAAU;QAAC8F,EAAE,EAAE;UAAEiC,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAAAvB,QAAA,GAC9CuC,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACE,MAAM,CAACwB,MAAM;MAAA;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC0B,GAAA,CAjIuBD,SAAS;AAAA0C,GAAA,GAAT1C,SAAS;AAAA,IAAAD,EAAA,EAAA2C,GAAA;AAAAC,YAAA,CAAA5C,EAAA;AAAA4C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}