// Entry point for backend Express app
require('dotenv').config();
const express = require('express');
const app = express();
const cors = require('cors');

// Configure CORS properly
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:3000', 'http://127.0.0.1:3001'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));


// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Debug middleware
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path}`, req.body);
  next();
});
const path = require('path');
// Serve /uploads untuk berita, /uploads/setting untuk logo setting
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/uploads/setting', express.static(path.join(__dirname, 'uploads/setting')));

// Routes

const kostumRouter = require('./routes/kostum');
app.use('/api/kostum', kostumRouter);

const postRoutes = require('./routes/postRoutes');
app.use('/api', postRoutes);
const adminRoutes = require('./routes/adminRoutes');
app.use('/api/admin', adminRoutes);
const savedRoutes = require('./routes/savedRoutes');
app.use('/api/posts', savedRoutes);
const categoryRoutes = require('./routes/categoryRoutes');
app.use('/api/categories', categoryRoutes);

// Import auth middleware
const { authMiddleware, adminMiddleware, authRoutes } = require('./middleware/authMiddleware');

// Auth routes
app.use('/api/auth', authRoutes);

// Settings routes
const settingsRoutes = require('./routes/settingsRoutes');
app.use('/api/settings', settingsRoutes);

// Test route
app.get('/api/test', (req, res) => {
  res.json({ message: 'Backend is working!', timestamp: new Date().toISOString() });
});

// Add saved posts route
app.get('/api/saved', (req, res) => {
  // Return dummy saved posts for now
  const savedPosts = [
    {
      id: 1,
      title: 'Berita Tersimpan 1',
      content: 'Ini adalah contoh berita yang telah disimpan oleh user.',
      image: 'https://source.unsplash.com/400x300/?news',
      category: 'Teknologi',
      category_name: 'Teknologi',
      category_color: '#3B82F6',
      date: new Date().toISOString(),
      created_at: new Date().toISOString(),
      views: 150,
      share: 25,
      likes: 45
    }
  ];
  res.json(savedPosts);
});

// Log semua route yang terdaftar
app._router && app._router.stack.forEach(function(r){
  if (r.route && r.route.path){
    console.log('ROUTE:', r.route.path)
  }
})

const PORT = process.env.PORT || 8000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
