// Entry point for backend Express app
require('dotenv').config();
const express = require('express');
const app = express();
const cors = require('cors');
const path = require('path');

// Configure CORS properly
app.use(cors({
  origin: [
    'http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173',
    'http://127.0.0.1:3000', 'http://127.0.0.1:3001', 'http://127.0.0.1:5173'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Debug middleware (only in development)
if (process.env.NODE_ENV !== 'production') {
  app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
  });
}

// Serve static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/uploads/setting', express.static(path.join(__dirname, 'uploads/setting')));

// Serve admin PHP files and assets
app.use('/admin/php', express.static(path.join(__dirname, '../frontend/src/pages/admin')));
app.use('/admin/assets', express.static(path.join(__dirname, '../frontend/src/pages/admin/assets')));

// Import controllers
const postController = require('./controller/postController');

// Admin routes (simple approach)
app.get('/admin/dashboard', (req, res) => {
  const queryString = req.url.includes('?') ? req.url.split('?')[1] : '';
  const redirectUrl = queryString ?
    `/admin/php/dashboard.php?${queryString}` :
    '/admin/php/dashboard.php';
  res.redirect(redirectUrl);
});

app.get('/admin/api/get_videos', (req, res) => {
  res.redirect('/admin/php/api.php?action=get_videos');
});

app.post('/admin/api/toggle_video_like', (req, res) => {
  const id = req.query.id;
  res.redirect(307, `/admin/php/api.php?action=toggle_video_like&id=${id}`);
});

app.post('/admin/api/increment_video_views', (req, res) => {
  const id = req.query.id;
  res.redirect(307, `/admin/php/api.php?action=increment_video_views&id=${id}`);
});

app.post('/admin/api/increment_video_share', (req, res) => {
  const id = req.query.id;
  res.redirect(307, `/admin/php/api.php?action=increment_video_share&id=${id}`);
});

// Frontend API v1 routes (clean API)
app.get('/api/v1/videos', postController.getVideos);
app.get('/api/v1/videos/:id', postController.getVideoById);
app.post('/api/v1/videos/:id/like', postController.toggleVideoLike);
app.post('/api/v1/videos/:id/share', postController.incrementVideoShare);
app.post('/api/v1/videos/:id/view', postController.incrementVideoView);

app.get('/api/v1/posts', postController.getPosts);
app.get('/api/v1/posts/:id', postController.getPostById);
app.post('/api/v1/posts/:id/share', postController.incrementShare);

// Categories endpoint
app.get('/api/v1/categories', (req, res) => {
  const categories = [
    { id: 1, name: 'Umum', color: '#6B7280' },
    { id: 2, name: 'Teknologi', color: '#3B82F6' },
    { id: 3, name: 'Bisnis', color: '#10B981' },
    { id: 4, name: 'Olahraga', color: '#F59E0B' },
    { id: 5, name: 'Hiburan', color: '#EF4444' },
    { id: 6, name: 'Politik', color: '#8B5CF6' },
    { id: 7, name: 'Kesehatan', color: '#06B6D4' }
  ];
  res.json({ success: true, data: categories });
});

// Saved posts endpoint
app.get('/api/v1/saved', (req, res) => {
  const savedPosts = [
    {
      id: 1,
      title: 'Berita Tersimpan 1',
      content: 'Ini adalah contoh berita yang telah disimpan oleh user.',
      image: 'https://source.unsplash.com/400x300/?news',
      category: 'Teknologi',
      category_name: 'Teknologi',
      category_color: '#3B82F6',
      date: new Date().toISOString(),
      created_at: new Date().toISOString(),
      views: 150,
      share: 25,
      likes: 45
    }
  ];
  res.json({ success: true, data: savedPosts });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Backend server is running!',
    timestamp: new Date().toISOString(),
    port: process.env.PORT || 3000,
    environment: process.env.NODE_ENV || 'development'
  });
});

// Legacy API test route
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Legacy API is working!',
    timestamp: new Date().toISOString(),
    note: 'Consider using /api/v1 for new endpoints'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Backend server running on port ${PORT}`);
  console.log(`📊 Admin Panel: http://localhost:${PORT}/admin/dashboard`);
  console.log(`🔗 API Health: http://localhost:${PORT}/health`);
  console.log(`📱 Frontend API: http://localhost:${PORT}/api/v1`);
});
