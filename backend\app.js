// Entry point for backend Express app
require('dotenv').config();
const express = require('express');
const app = express();
const cors = require('cors');
const path = require('path');

// Configure CORS properly
app.use(cors({
  origin: [
    'http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173',
    'http://127.0.0.1:3000', 'http://127.0.0.1:3001', 'http://127.0.0.1:5173'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Debug middleware (only in development)
if (process.env.NODE_ENV !== 'production') {
  app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
  });
}

// Serve static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/uploads/setting', express.static(path.join(__dirname, 'uploads/setting')));

// Serve admin PHP files and assets
app.use('/admin/php', express.static(path.join(__dirname, '../frontend/src/pages/admin')));
app.use('/admin/assets', express.static(path.join(__dirname, '../frontend/src/pages/admin/assets')));

// Import route modules
const adminPanelRoutes = require('./routes/adminPanelRoutes');
const frontendRoutes = require('./routes/frontendRoutes');

// Legacy routes (untuk backward compatibility)
const kostumRouter = require('./routes/kostum');
const adminRoutes = require('./routes/adminRoutes');
const savedRoutes = require('./routes/savedRoutes');
const categoryRoutes = require('./routes/categoryRoutes');
const settingsRoutes = require('./routes/settingsRoutes');

// Apply routes
app.use('/admin', adminPanelRoutes);           // Admin panel routes
app.use('/api/v1', frontendRoutes);            // Frontend API routes (new clean API)
app.use('/api/kostum', kostumRouter);          // Legacy kostum routes
app.use('/api/admin', adminRoutes);            // Legacy admin routes
app.use('/api/posts', savedRoutes);            // Legacy saved posts routes
app.use('/api/categories', categoryRoutes);    // Legacy category routes
app.use('/api/settings', settingsRoutes);     // Legacy settings routes

// Import auth middleware
try {
  const { authRoutes } = require('./middleware/authMiddleware');
  app.use('/api/auth', authRoutes);
} catch (error) {
  console.log('Auth middleware not found, skipping...');
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Backend server is running!',
    timestamp: new Date().toISOString(),
    port: process.env.PORT || 3000,
    environment: process.env.NODE_ENV || 'development'
  });
});

// Legacy API test route
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Legacy API is working!',
    timestamp: new Date().toISOString(),
    note: 'Consider using /api/v1 for new endpoints'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Backend server running on port ${PORT}`);
  console.log(`📊 Admin Panel: http://localhost:${PORT}/admin/dashboard`);
  console.log(`🔗 API Health: http://localhost:${PORT}/health`);
  console.log(`📱 Frontend API: http://localhost:${PORT}/api/v1`);
});
