<!-- Dashboard Overview -->
<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-800">Selamat Datang, Admin!</h1>
                <p class="text-gray-600 mt-2"><PERSON><PERSON><PERSON> konten berita Anda dengan mudah dan efisien</p>
            </div>
            <div class="hidden md:block">
                <i class="fas fa-chart-line text-6xl text-primary opacity-20"></i>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="bg-white rounded-xl shadow-sm p-6 card-hover border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-2xl font-bold text-gray-900" data-stat="total-users">
                        <i class="fas fa-spinner fa-spin text-lg"></i>
                    </p>
                </div>
            </div>
        </div>

        <!-- Total News -->
        <div class="bg-white rounded-xl shadow-sm p-6 card-hover border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-newspaper text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Berita</p>
                    <p class="text-2xl font-bold text-gray-900" data-stat="total-news">
                        <i class="fas fa-spinner fa-spin text-lg"></i>
                    </p>
                </div>
            </div>
        </div>

        <!-- Page Views -->
        <div class="bg-white rounded-xl shadow-sm p-6 card-hover border-l-4 border-yellow-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-eye text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Views</p>
                    <p class="text-2xl font-bold text-gray-900" data-stat="page-views">
                        <i class="fas fa-spinner fa-spin text-lg"></i>
                    </p>
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="bg-white rounded-xl shadow-sm p-6 card-hover border-l-4 border-purple-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-tags text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Kategori</p>
                    <p class="text-2xl font-bold text-gray-900" data-stat="total-categories">
                        <i class="fas fa-spinner fa-spin text-lg"></i>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Additional Stats Row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Published News -->
        <div class="bg-white rounded-xl shadow-sm p-6 card-hover border-l-4 border-emerald-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-emerald-100 text-emerald-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Published</p>
                    <p class="text-2xl font-bold text-gray-900" data-stat="published-news">
                        <i class="fas fa-spinner fa-spin text-lg"></i>
                    </p>
                </div>
            </div>
        </div>

        <!-- Draft News -->
        <div class="bg-white rounded-xl shadow-sm p-6 card-hover border-l-4 border-orange-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                    <i class="fas fa-edit text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Draft</p>
                    <p class="text-2xl font-bold text-gray-900" data-stat="draft-news">
                        <i class="fas fa-spinner fa-spin text-lg"></i>
                    </p>
                </div>
            </div>
        </div>

        <!-- Total Likes -->
        <div class="bg-white rounded-xl shadow-sm p-6 card-hover border-l-4 border-pink-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-pink-100 text-pink-600">
                    <i class="fas fa-heart text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Likes</p>
                    <p class="text-2xl font-bold text-gray-900" data-stat="total-likes">
                        <i class="fas fa-spinner fa-spin text-lg"></i>
                    </p>
                </div>
            </div>
        </div>

        <!-- Saved Posts -->
        <div class="bg-white rounded-xl shadow-sm p-6 card-hover border-l-4 border-indigo-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-indigo-100 text-indigo-600">
                    <i class="fas fa-bookmark text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Saved Posts</p>
                    <p class="text-2xl font-bold text-gray-900" data-stat="saved-posts">
                        <i class="fas fa-spinner fa-spin text-lg"></i>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent News -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-800">Berita Terbaru</h3>
                <a href="?page=news" class="text-primary hover:text-blue-700 text-sm font-medium">
                    Lihat Semua <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            <div id="recent-news" class="space-y-4">
                <!-- Will be loaded via JavaScript -->
                <div class="animate-pulse">
                    <div class="flex space-x-4">
                        <div class="w-16 h-16 bg-gray-200 rounded-lg"></div>
                        <div class="flex-1 space-y-2">
                            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Popular Posts -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-800">Berita Populer</h3>
                <a href="?page=news" class="text-primary hover:text-blue-700 text-sm font-medium">
                    Lihat Semua <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            <div id="popular-posts" class="space-y-4">
                <!-- Will be loaded via JavaScript -->
                <div class="animate-pulse">
                    <div class="flex space-x-4">
                        <div class="w-16 h-16 bg-gray-200 rounded-lg"></div>
                        <div class="flex-1 space-y-2">
                            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-6">Aksi Cepat</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="?page=add-news" class="flex items-center p-4 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                <i class="fas fa-plus text-xl mr-3"></i>
                <div>
                    <div class="font-medium">Tambah Berita</div>
                    <div class="text-sm opacity-90">Buat berita baru</div>
                </div>
            </a>
            
            <a href="?page=categories" class="flex items-center p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                <i class="fas fa-tags text-xl mr-3"></i>
                <div>
                    <div class="font-medium">Kelola Kategori</div>
                    <div class="text-sm opacity-90">Atur kategori berita</div>
                </div>
            </a>
            
            <a href="?page=settings" class="flex items-center p-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200">
                <i class="fas fa-cog text-xl mr-3"></i>
                <div>
                    <div class="font-medium">Pengaturan</div>
                    <div class="text-sm opacity-90">Konfigurasi website</div>
                </div>
            </a>
        </div>
    </div>
</div>

<script>
// Load dashboard data
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard loading...');
    console.log('API_BASE:', API_BASE);

    // Load data with delay to ensure proper loading
    setTimeout(() => {
        loadDashboardStats();
        loadRecentNews();
        loadPopularPosts();
    }, 500);
});

// Load recent news
async function loadRecentNews() {
    try {
        console.log('Loading recent news...');
        const data = await safeFetch(`${API_BASE}?action=get_news&limit=5`);
        const container = document.getElementById('recent-news');

        console.log('Recent news data:', data);

        if (data.success && data.data && data.data.length > 0) {
            container.innerHTML = data.data.map(news => `
                <div class="flex items-center space-x-4 p-3 hover:bg-gray-50 rounded-lg transition-colors duration-200 cursor-pointer" onclick="editNews(${news.id})">
                    <div class="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                        ${news.image ?
                            `<img src="${getImageUrl(news.image)}" alt="${news.title}" class="w-full h-full object-cover" onerror="this.parentElement.innerHTML='<div class=\\'w-full h-full flex items-center justify-center text-gray-400\\'><i class=\\'fas fa-image\\'></i></div>'">` :
                            `<div class="w-full h-full flex items-center justify-center text-gray-400">
                                <i class="fas fa-image"></i>
                            </div>`
                        }
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-medium text-gray-900 truncate" title="${news.title}">${news.title}</h4>
                        <p class="text-xs text-gray-500 mt-1">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${news.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                ${news.status === 'published' ? 'Published' : 'Draft'}
                            </span>
                            <span class="ml-2">${formatDate(news.created_at)}</span>
                        </p>
                        <p class="text-xs text-gray-400 mt-1">
                            <i class="fas fa-eye mr-1"></i>${news.views || 0} views
                            <i class="fas fa-heart ml-2 mr-1"></i>${news.likes || 0} likes
                        </p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            `).join('');
        } else {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">Belum ada berita</p>';
        }
    } catch (error) {
        console.error('Error loading recent news:', error);
        document.getElementById('recent-news').innerHTML = '<p class="text-red-500 text-center py-4">Error memuat berita terbaru</p>';
    }
}

// Load popular posts
async function loadPopularPosts() {
    try {
        console.log('Loading popular posts...');
        const data = await safeFetch(`${API_BASE}?action=get_popular_posts&limit=5`);
        const container = document.getElementById('popular-posts');

        console.log('Popular posts data:', data);

        if (data.success && data.data && data.data.length > 0) {
            container.innerHTML = data.data.map((post, index) => `
                <div class="flex items-center space-x-4 p-3 hover:bg-gray-50 rounded-lg transition-colors duration-200 cursor-pointer" onclick="editNews(${post.id})">
                    <div class="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">
                        ${index + 1}
                    </div>
                    <div class="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                        ${post.image ?
                            `<img src="${getImageUrl(post.image)}" alt="${post.title}" class="w-full h-full object-cover" onerror="this.parentElement.innerHTML='<div class=\\'w-full h-full flex items-center justify-center text-gray-400\\'><i class=\\'fas fa-image\\'></i></div>'">` :
                            `<div class="w-full h-full flex items-center justify-center text-gray-400">
                                <i class="fas fa-image"></i>
                            </div>`
                        }
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-medium text-gray-900 truncate" title="${post.title}">${post.title}</h4>
                        <p class="text-xs text-gray-500 mt-1">
                            <i class="fas fa-eye mr-1"></i>${post.views || 0} views
                            <i class="fas fa-heart ml-3 mr-1"></i>${post.likes || 0} likes
                            <i class="fas fa-share ml-3 mr-1"></i>${post.share || 0} shares
                        </p>
                        <p class="text-xs text-gray-400 mt-1">
                            ${post.category_name || 'Umum'} • ${formatDate(post.created_at)}
                        </p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            `).join('');
        } else {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">Belum ada data</p>';
        }
    } catch (error) {
        console.error('Error loading popular posts:', error);
        document.getElementById('popular-posts').innerHTML = '<p class="text-red-500 text-center py-4">Error memuat berita populer</p>';
    }
}

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        console.log('Loading dashboard stats...');
        const data = await safeFetch(`${API_BASE}?action=get_stats`);

        console.log('Dashboard stats response:', data);

        if (data.success && data.data) {
            const stats = data.data;

            // Update stat elements with animation
            const statElements = {
                'total-users': stats.total_users || 0,
                'total-news': stats.total_news || 0,
                'page-views': stats.page_views || 0,
                'total-categories': stats.total_categories || 0,
                'published-news': stats.published_news || 0,
                'draft-news': stats.draft_news || 0,
                'total-likes': stats.total_likes || 0,
                'saved-posts': stats.saved_posts || 0
            };

            Object.entries(statElements).forEach(([key, value]) => {
                const element = document.querySelector(`[data-stat="${key}"]`);
                if (element) {
                    // Add loading animation before updating
                    element.style.opacity = '0.5';

                    setTimeout(() => {
                        element.innerHTML = new Intl.NumberFormat('id-ID').format(value);
                        element.style.opacity = '1';

                        // Add a subtle animation
                        element.style.transform = 'scale(1.05)';
                        setTimeout(() => {
                            element.style.transform = 'scale(1)';
                        }, 200);
                    }, 300);
                }
            });

            console.log('Dashboard stats loaded:', stats);
        } else {
            console.error('Failed to load stats:', data);
            showErrorOnStats();
        }
    } catch (error) {
        console.error('Error loading dashboard stats:', error);
        showErrorOnStats();
    }
}

// Show error state on stats cards
function showErrorOnStats() {
    const statElements = [
        'total-users', 'total-news', 'page-views', 'total-categories',
        'published-news', 'draft-news', 'total-likes', 'saved-posts'
    ];

    statElements.forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
            element.innerHTML = '<span class="text-red-500">Error</span>';
        }
    });
}

// Helper function to format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
    });
}

// Helper function to get correct image URL - Use root/uploads
function getImageUrl(imagePath) {
    if (!imagePath) return null;

    console.log('Dashboard - Processing image path:', imagePath);

    // Jika sudah URL lengkap, gunakan langsung
    if (imagePath.startsWith('http')) {
        console.log('Dashboard - Using full URL:', imagePath);
        return imagePath;
    }

    // Extract filename from any path format
    let filename = '';

    if (imagePath.startsWith('/react-news/uploads/')) {
        filename = imagePath.replace('/react-news/uploads/', '');
    } else if (imagePath.startsWith('/react-news/frontend/uploads/')) {
        filename = imagePath.replace('/react-news/frontend/uploads/', '');
    } else if (imagePath.startsWith('/uploads/')) {
        filename = imagePath.replace('/uploads/', '');
    } else if (imagePath.startsWith('assets/news/')) {
        filename = imagePath.replace('assets/news/', '');
    } else if (!imagePath.includes('/')) {
        // Just filename
        filename = imagePath;
    } else {
        // Extract filename from any other path
        filename = imagePath.split('/').pop();
    }

    // Use absolute path to root/uploads
    const url = `http://localhost/react-news/uploads/${filename}`;
    console.log('Dashboard - Using root/uploads path:', url);
    return url;
}

// Navigation functions
function editNews(id) {
    window.location.href = `?page=edit-news&id=${id}`;
}

// Auto refresh dashboard stats every 30 seconds
setInterval(() => {
    loadDashboardStats();
}, 30000);
</script>
