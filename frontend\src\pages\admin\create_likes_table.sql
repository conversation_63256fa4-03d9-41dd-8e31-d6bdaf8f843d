-- Create post_likes table for tracking likes by IP address and user
-- Run this in your MySQL database

USE react_news;

-- Create post_likes table if not exists
CREATE TABLE IF NOT EXISTS `post_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `liked_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_like` (`post_id`, `ip_address`),
  KEY `post_id` (`post_id`),
  <PERSON><PERSON>Y `user_id` (`user_id`),
  KEY `ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add foreign key constraints if they don't exist
-- Note: Run these separately if the table already exists
-- ALTER TABLE `post_likes` ADD CONSTRAINT `fk_post_likes_post` <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (`post_id`) REFERENCES `posts`(`id`) ON DELETE CASCADE;
-- ALTER TABLE `post_likes` ADD CONSTRAINT `fk_post_likes_user` FOREIGN KEY (`user_id`) REFERENCES `admin`(`id`) ON DELETE SET NULL;

SELECT 'post_likes table created successfully!' as message;
