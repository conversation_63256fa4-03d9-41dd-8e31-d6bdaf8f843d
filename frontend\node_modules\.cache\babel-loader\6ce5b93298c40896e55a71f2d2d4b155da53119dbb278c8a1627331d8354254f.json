{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\components\\\\VideoFeed.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, IconButton, Avatar, Chip, useTheme, useMediaQuery } from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Sample video data with YouTube URLs (in real app, this would come from API)\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst sampleVideos = [{\n  id: 1,\n  videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&mute=1&controls=0&loop=1&playlist=dQw4w9WgXcQ',\n  youtubeId: 'dQw4w9WgXcQ',\n  title: 'Breaking News: Teknologi AI Terbaru',\n  description: 'Perkembangan teknologi AI yang mengubah dunia digital saat ini. Simak ulasan lengkapnya dalam video berita eksklusif ini!',\n  tags: ['teknologi', 'ai', 'digital', 'inovasi', 'berita'],\n  author: {\n    name: 'Tech Reporter',\n    avatar: 'https://i.pravatar.cc/150?img=1',\n    verified: true\n  },\n  stats: {\n    likes: 1250,\n    comments: 89,\n    shares: 45\n  },\n  uploadDate: '2024-01-15T10:30:00Z',\n  duration: '02:45',\n  category: 'Teknologi'\n}, {\n  id: 2,\n  videoUrl: 'https://www.youtube.com/embed/ScMzIvxBSi4?autoplay=1&mute=1&controls=0&loop=1&playlist=ScMzIvxBSi4',\n  youtubeId: 'ScMzIvxBSi4',\n  title: 'Olahraga: Final Sepak Bola Spektakuler',\n  description: 'Highlight pertandingan final yang sangat menegangkan. Gol spektakuler di menit terakhir yang mengubah segalanya!',\n  tags: ['olahraga', 'sepakbola', 'final', 'highlight', 'berita'],\n  author: {\n    name: 'Sports News',\n    avatar: 'https://i.pravatar.cc/150?img=2',\n    verified: true\n  },\n  stats: {\n    likes: 2100,\n    comments: 156,\n    shares: 78\n  },\n  uploadDate: '2024-01-15T14:20:00Z',\n  duration: '03:12',\n  category: 'Olahraga'\n}, {\n  id: 3,\n  videoUrl: 'https://www.youtube.com/embed/jNQXAC9IVRw?autoplay=1&mute=1&controls=0&loop=1&playlist=jNQXAC9IVRw',\n  youtubeId: 'jNQXAC9IVRw',\n  title: 'Kuliner: Resep Masakan Tradisional Nusantara',\n  description: 'Cara membuat masakan tradisional yang lezat dan mudah. Cocok untuk pemula yang ingin belajar memasak!',\n  tags: ['kuliner', 'resep', 'tradisional', 'masakan', 'nusantara'],\n  author: {\n    name: 'Chef Indonesia',\n    avatar: 'https://i.pravatar.cc/150?img=3',\n    verified: false\n  },\n  stats: {\n    likes: 890,\n    comments: 67,\n    shares: 34\n  },\n  uploadDate: '2024-01-15T16:45:00Z',\n  duration: '04:30',\n  category: 'Kuliner'\n}, {\n  id: 4,\n  videoUrl: 'https://www.youtube.com/embed/M7lc1UVf-VE?autoplay=1&mute=1&controls=0&loop=1&playlist=M7lc1UVf-VE',\n  youtubeId: 'M7lc1UVf-VE',\n  title: 'Politik: Update Terkini Pemerintahan',\n  description: 'Berita politik terbaru dan analisis mendalam tentang kebijakan pemerintah yang berdampak pada masyarakat.',\n  tags: ['politik', 'pemerintah', 'kebijakan', 'berita', 'analisis'],\n  author: {\n    name: 'Political Analyst',\n    avatar: 'https://i.pravatar.cc/150?img=4',\n    verified: true\n  },\n  stats: {\n    likes: 1580,\n    comments: 234,\n    shares: 89\n  },\n  uploadDate: '2024-01-15T18:30:00Z',\n  duration: '05:15',\n  category: 'Politik'\n}];\n\n// Individual Video Item Component\nfunction VideoItem({\n  video,\n  isActive,\n  onVideoClick\n}) {\n  _s();\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const videoRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // TikTok aspect ratio: 9:16 (vertical)\n  const videoHeight = isMobile ? '100vh' : '80vh';\n  const videoWidth = isMobile ? '100vw' : '45vh'; // 9:16 ratio\n\n  useEffect(() => {\n    if (isActive && videoRef.current) {\n      videoRef.current.play();\n      setIsPlaying(true);\n    } else if (videoRef.current) {\n      videoRef.current.pause();\n      setIsPlaying(false);\n    }\n  }, [isActive]);\n  const handleVideoClick = () => {\n    if (videoRef.current) {\n      if (isPlaying) {\n        videoRef.current.pause();\n        setIsPlaying(false);\n      } else {\n        videoRef.current.play();\n        setIsPlaying(true);\n      }\n    }\n  };\n  const handleLike = () => {\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n  };\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n  const handleShare = () => {\n    console.log('Share video:', video.id);\n  };\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"video\", {\n      ref: videoRef,\n      src: video.videoUrl,\n      style: {\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover',\n        cursor: 'pointer'\n      },\n      onClick: handleVideoClick,\n      loop: true,\n      muted: true,\n      playsInline: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), !isPlaying && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: '50%',\n        left: '50%',\n        transform: 'translate(-50%, -50%)',\n        zIndex: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handleVideoClick,\n        sx: {\n          bgcolor: 'rgba(0, 0, 0, 0.5)',\n          color: 'white',\n          '&:hover': {\n            bgcolor: 'rgba(0, 0, 0, 0.7)'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(PlayArrowIcon, {\n          sx: {\n            fontSize: 48\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: video.author.avatar,\n          sx: {\n            width: 48,\n            height: 48,\n            border: '2px solid white',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), video.author.verified && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            bgcolor: '#1976d2',\n            borderRadius: '50%',\n            width: 20,\n            height: 20,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mt: -1,\n            border: '2px solid white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: 10,\n              fontWeight: 'bold'\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleLike,\n          sx: {\n            color: isLiked ? '#e91e63' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isLiked ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleComment,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.comments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSave,\n          sx: {\n            color: isSaved ? '#ffc107' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isSaved ? /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(BookmarkBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: \"Simpan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleShare,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.shares\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.8)',\n          fontSize: 12,\n          mb: 1\n        },\n        children: [formatDate(video.uploadDate), \" \\u2022 \", video.duration]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'white',\n          fontWeight: 'bold',\n          fontSize: 16,\n          mb: 1,\n          lineHeight: 1.3\n        },\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: 14,\n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        },\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: video.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(Chip, {\n          label: `#${tag}`,\n          size: \"small\",\n          sx: {\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            fontSize: 11,\n            height: 24,\n            '&:hover': {\n              bgcolor: 'rgba(255, 255, 255, 0.3)'\n            }\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this);\n}\n\n// Main VideoFeed Component\n_s(VideoItem, \"TVqX1HTPFaWU7MX/nbazUB4v2Zo=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = VideoItem;\nexport default function VideoFeed() {\n  _s2();\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos] = useState(sampleVideos);\n\n  // Handle scroll to change videos (simplified for demo)\n  useEffect(() => {\n    const handleScroll = e => {\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n    };\n    window.addEventListener('wheel', handleScroll);\n    return () => window.removeEventListener('wheel', handleScroll);\n  }, [currentVideoIndex, videos.length]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative'\n    },\n    children: [videos.map((video, index) => /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n        transition: 'transform 0.3s ease-in-out'\n      },\n      children: /*#__PURE__*/_jsxDEV(VideoItem, {\n        video: video,\n        isActive: index === currentVideoIndex\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this)\n    }, video.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 20,\n        right: 20,\n        bgcolor: 'rgba(0, 0, 0, 0.5)',\n        borderRadius: 2,\n        px: 2,\n        py: 1,\n        zIndex: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'white',\n          fontSize: 12\n        },\n        children: [currentVideoIndex + 1, \" / \", videos.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 415,\n    columnNumber: 5\n  }, this);\n}\n_s2(VideoFeed, \"kxbY2fDSroOlTD0JWPG87QVsmBw=\");\n_c2 = VideoFeed;\nvar _c, _c2;\n$RefreshReg$(_c, \"VideoItem\");\n$RefreshReg$(_c2, \"VideoFeed\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "IconButton", "Avatar", "Chip", "useTheme", "useMediaQuery", "FavoriteIcon", "FavoriteBorderIcon", "ChatBubbleOutlineIcon", "BookmarkBorderIcon", "BookmarkIcon", "ShareIcon", "PlayArrowIcon", "PauseIcon", "jsxDEV", "_jsxDEV", "sampleVideos", "id", "videoUrl", "youtubeId", "title", "description", "tags", "author", "name", "avatar", "verified", "stats", "likes", "comments", "shares", "uploadDate", "duration", "category", "VideoItem", "video", "isActive", "onVideoClick", "_s", "isPlaying", "setIsPlaying", "isLiked", "setIsLiked", "isSaved", "setIsSaved", "setLikes", "videoRef", "theme", "isMobile", "breakpoints", "down", "videoHeight", "videoWidth", "current", "play", "pause", "handleVideoClick", "handleLike", "prev", "handleSave", "handleShare", "console", "log", "handleComment", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "sx", "position", "height", "width", "max<PERSON><PERSON><PERSON>", "mx", "bgcolor", "display", "alignItems", "justifyContent", "overflow", "children", "ref", "src", "style", "objectFit", "cursor", "onClick", "loop", "muted", "playsInline", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "left", "transform", "zIndex", "color", "fontSize", "right", "bottom", "flexDirection", "gap", "border", "borderRadius", "mt", "fontWeight", "toFixed", "mb", "lineHeight", "WebkitLineClamp", "WebkitBoxOrient", "flexWrap", "map", "tag", "index", "label", "size", "_c", "VideoFeed", "_s2", "currentVideoIndex", "setCurrentVideoIndex", "videos", "handleScroll", "e", "deltaY", "length", "window", "addEventListener", "removeEventListener", "transition", "px", "py", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/components/VideoFeed.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Avatar,\n  Chip,\n  useTheme,\n  useMediaQuery\n} from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Sample video data with YouTube URLs (in real app, this would come from API)\nconst sampleVideos = [\n  {\n    id: 1,\n    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&mute=1&controls=0&loop=1&playlist=dQw4w9WgXcQ',\n    youtubeId: 'dQw4w9WgXcQ',\n    title: 'Breaking News: Teknologi AI Terbaru',\n    description: 'Perkembangan teknologi AI yang mengubah dunia digital saat ini. Simak ulasan lengkapnya dalam video berita eksklusif ini!',\n    tags: ['teknologi', 'ai', 'digital', 'inovasi', 'berita'],\n    author: {\n      name: 'Tech Reporter',\n      avatar: 'https://i.pravatar.cc/150?img=1',\n      verified: true\n    },\n    stats: {\n      likes: 1250,\n      comments: 89,\n      shares: 45\n    },\n    uploadDate: '2024-01-15T10:30:00Z',\n    duration: '02:45',\n    category: 'Teknologi'\n  },\n  {\n    id: 2,\n    videoUrl: 'https://www.youtube.com/embed/ScMzIvxBSi4?autoplay=1&mute=1&controls=0&loop=1&playlist=ScMzIvxBSi4',\n    youtubeId: 'ScMzIvxBSi4',\n    title: 'Olahraga: Final Sepak Bola Spektakuler',\n    description: 'Highlight pertandingan final yang sangat menegangkan. Gol spektakuler di menit terakhir yang mengubah segalanya!',\n    tags: ['olahraga', 'sepakbola', 'final', 'highlight', 'berita'],\n    author: {\n      name: 'Sports News',\n      avatar: 'https://i.pravatar.cc/150?img=2',\n      verified: true\n    },\n    stats: {\n      likes: 2100,\n      comments: 156,\n      shares: 78\n    },\n    uploadDate: '2024-01-15T14:20:00Z',\n    duration: '03:12',\n    category: 'Olahraga'\n  },\n  {\n    id: 3,\n    videoUrl: 'https://www.youtube.com/embed/jNQXAC9IVRw?autoplay=1&mute=1&controls=0&loop=1&playlist=jNQXAC9IVRw',\n    youtubeId: 'jNQXAC9IVRw',\n    title: 'Kuliner: Resep Masakan Tradisional Nusantara',\n    description: 'Cara membuat masakan tradisional yang lezat dan mudah. Cocok untuk pemula yang ingin belajar memasak!',\n    tags: ['kuliner', 'resep', 'tradisional', 'masakan', 'nusantara'],\n    author: {\n      name: 'Chef Indonesia',\n      avatar: 'https://i.pravatar.cc/150?img=3',\n      verified: false\n    },\n    stats: {\n      likes: 890,\n      comments: 67,\n      shares: 34\n    },\n    uploadDate: '2024-01-15T16:45:00Z',\n    duration: '04:30',\n    category: 'Kuliner'\n  },\n  {\n    id: 4,\n    videoUrl: 'https://www.youtube.com/embed/M7lc1UVf-VE?autoplay=1&mute=1&controls=0&loop=1&playlist=M7lc1UVf-VE',\n    youtubeId: 'M7lc1UVf-VE',\n    title: 'Politik: Update Terkini Pemerintahan',\n    description: 'Berita politik terbaru dan analisis mendalam tentang kebijakan pemerintah yang berdampak pada masyarakat.',\n    tags: ['politik', 'pemerintah', 'kebijakan', 'berita', 'analisis'],\n    author: {\n      name: 'Political Analyst',\n      avatar: 'https://i.pravatar.cc/150?img=4',\n      verified: true\n    },\n    stats: {\n      likes: 1580,\n      comments: 234,\n      shares: 89\n    },\n    uploadDate: '2024-01-15T18:30:00Z',\n    duration: '05:15',\n    category: 'Politik'\n  }\n];\n\n// Individual Video Item Component\nfunction VideoItem({ video, isActive, onVideoClick }) {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const videoRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // TikTok aspect ratio: 9:16 (vertical)\n  const videoHeight = isMobile ? '100vh' : '80vh';\n  const videoWidth = isMobile ? '100vw' : '45vh'; // 9:16 ratio\n\n  useEffect(() => {\n    if (isActive && videoRef.current) {\n      videoRef.current.play();\n      setIsPlaying(true);\n    } else if (videoRef.current) {\n      videoRef.current.pause();\n      setIsPlaying(false);\n    }\n  }, [isActive]);\n\n  const handleVideoClick = () => {\n    if (videoRef.current) {\n      if (isPlaying) {\n        videoRef.current.pause();\n        setIsPlaying(false);\n      } else {\n        videoRef.current.play();\n        setIsPlaying(true);\n      }\n    }\n  };\n\n  const handleLike = () => {\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n  };\n\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n\n  const handleShare = () => {\n    console.log('Share video:', video.id);\n  };\n\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n\n  return (\n    <Box sx={{\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden'\n    }}>\n      {/* Video Element */}\n      <video\n        ref={videoRef}\n        src={video.videoUrl}\n        style={{\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover',\n          cursor: 'pointer'\n        }}\n        onClick={handleVideoClick}\n        loop\n        muted\n        playsInline\n      />\n\n      {/* Play/Pause Overlay */}\n      {!isPlaying && (\n        <Box sx={{\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          zIndex: 2\n        }}>\n          <IconButton\n            onClick={handleVideoClick}\n            sx={{\n              bgcolor: 'rgba(0, 0, 0, 0.5)',\n              color: 'white',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.7)' }\n            }}\n          >\n            <PlayArrowIcon sx={{ fontSize: 48 }} />\n          </IconButton>\n        </Box>\n      )}\n\n      {/* Right Side Actions (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      }}>\n        {/* Profile Avatar */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <Avatar\n            src={video.author.avatar}\n            sx={{ \n              width: 48, \n              height: 48, \n              border: '2px solid white',\n              cursor: 'pointer'\n            }}\n          />\n          {video.author.verified && (\n            <Box sx={{\n              bgcolor: '#1976d2',\n              borderRadius: '50%',\n              width: 20,\n              height: 20,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mt: -1,\n              border: '2px solid white'\n            }}>\n              <Typography sx={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>✓</Typography>\n            </Box>\n          )}\n        </Box>\n\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleLike}\n            sx={{\n              color: isLiked ? '#e91e63' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes}\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleComment}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.comments}\n          </Typography>\n        </Box>\n\n        {/* Save Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleSave}\n            sx={{\n              color: isSaved ? '#ffc107' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            Simpan\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleShare}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.shares}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Bottom Info (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      }}>\n        {/* Upload Date and Duration */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.8)', \n          fontSize: 12, \n          mb: 1 \n        }}>\n          {formatDate(video.uploadDate)} • {video.duration}\n        </Typography>\n\n        {/* Title */}\n        <Typography sx={{ \n          color: 'white', \n          fontWeight: 'bold', \n          fontSize: 16, \n          mb: 1,\n          lineHeight: 1.3\n        }}>\n          {video.title}\n        </Typography>\n\n        {/* Description */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.9)', \n          fontSize: 14, \n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        }}>\n          {video.description}\n        </Typography>\n\n        {/* Tags */}\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          {video.tags.map((tag, index) => (\n            <Chip\n              key={index}\n              label={`#${tag}`}\n              size=\"small\"\n              sx={{\n                bgcolor: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                fontSize: 11,\n                height: 24,\n                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' }\n              }}\n            />\n          ))}\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\n// Main VideoFeed Component\nexport default function VideoFeed() {\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos] = useState(sampleVideos);\n\n  // Handle scroll to change videos (simplified for demo)\n  useEffect(() => {\n    const handleScroll = (e) => {\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n    };\n\n    window.addEventListener('wheel', handleScroll);\n    return () => window.removeEventListener('wheel', handleScroll);\n  }, [currentVideoIndex, videos.length]);\n\n  return (\n    <Box sx={{\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative'\n    }}>\n      {videos.map((video, index) => (\n        <Box\n          key={video.id}\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n            transition: 'transform 0.3s ease-in-out'\n          }}\n        >\n          <VideoItem\n            video={video}\n            isActive={index === currentVideoIndex}\n          />\n        </Box>\n      ))}\n\n      {/* Video Counter */}\n      <Box sx={{\n        position: 'absolute',\n        top: 20,\n        right: 20,\n        bgcolor: 'rgba(0, 0, 0, 0.5)',\n        borderRadius: 2,\n        px: 2,\n        py: 1,\n        zIndex: 4\n      }}>\n        <Typography sx={{ color: 'white', fontSize: 12 }}>\n          {currentVideoIndex + 1} / {videos.length}\n        </Typography>\n      </Box>\n    </Box>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,SAAS,MAAM,2BAA2B;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG,CACnB;EACEC,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,oGAAoG;EAC9GC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,qCAAqC;EAC5CC,WAAW,EAAE,2HAA2H;EACxIC,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;EACzDC,MAAM,EAAE;IACNC,IAAI,EAAE,eAAe;IACrBC,MAAM,EAAE,iCAAiC;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE,sBAAsB;EAClCC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,oGAAoG;EAC9GC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,wCAAwC;EAC/CC,WAAW,EAAE,kHAAkH;EAC/HC,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC;EAC/DC,MAAM,EAAE;IACNC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE,iCAAiC;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE,sBAAsB;EAClCC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,oGAAoG;EAC9GC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,8CAA8C;EACrDC,WAAW,EAAE,uGAAuG;EACpHC,IAAI,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,CAAC;EACjEC,MAAM,EAAE;IACNC,IAAI,EAAE,gBAAgB;IACtBC,MAAM,EAAE,iCAAiC;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE,sBAAsB;EAClCC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEhB,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,oGAAoG;EAC9GC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,sCAAsC;EAC7CC,WAAW,EAAE,2GAA2G;EACxHC,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC;EAClEC,MAAM,EAAE;IACNC,IAAI,EAAE,mBAAmB;IACzBC,MAAM,EAAE,iCAAiC;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAE;EACV,CAAC;EACDC,UAAU,EAAE,sBAAsB;EAClCC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACZ,CAAC,CACF;;AAED;AACA,SAASC,SAASA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,KAAK,EAAEiB,QAAQ,CAAC,GAAGjD,QAAQ,CAACuC,KAAK,CAACR,KAAK,CAACC,KAAK,CAAC;EACrD,MAAMkB,QAAQ,GAAGjD,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMkD,KAAK,GAAG3C,QAAQ,CAAC,CAAC;EACxB,MAAM4C,QAAQ,GAAG3C,aAAa,CAAC0C,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAMC,WAAW,GAAGH,QAAQ,GAAG,OAAO,GAAG,MAAM;EAC/C,MAAMI,UAAU,GAAGJ,QAAQ,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC;;EAEhDlD,SAAS,CAAC,MAAM;IACd,IAAIsC,QAAQ,IAAIU,QAAQ,CAACO,OAAO,EAAE;MAChCP,QAAQ,CAACO,OAAO,CAACC,IAAI,CAAC,CAAC;MACvBd,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,MAAM,IAAIM,QAAQ,CAACO,OAAO,EAAE;MAC3BP,QAAQ,CAACO,OAAO,CAACE,KAAK,CAAC,CAAC;MACxBf,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACJ,QAAQ,CAAC,CAAC;EAEd,MAAMoB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIV,QAAQ,CAACO,OAAO,EAAE;MACpB,IAAId,SAAS,EAAE;QACbO,QAAQ,CAACO,OAAO,CAACE,KAAK,CAAC,CAAC;QACxBf,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,MAAM;QACLM,QAAQ,CAACO,OAAO,CAACC,IAAI,CAAC,CAAC;QACvBd,YAAY,CAAC,IAAI,CAAC;MACpB;IACF;EACF,CAAC;EAED,MAAMiB,UAAU,GAAGA,CAAA,KAAM;IACvBf,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBI,QAAQ,CAACa,IAAI,IAAIjB,OAAO,GAAGiB,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;EACjD,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBf,UAAU,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,MAAMiB,WAAW,GAAGA,CAAA,KAAM;IACxBC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE3B,KAAK,CAAClB,EAAE,CAAC;EACvC,CAAC;EAED,MAAM8C,aAAa,GAAGA,CAAA,KAAM;IAC1BF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE3B,KAAK,CAAClB,EAAE,CAAC;EACnD,CAAC;EAED,MAAM+C,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE,OAAO,aAAa;IACxC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,cAAc;IAClE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,EAAE,CAAC,aAAa;EACjD,CAAC;EAED,oBACEzD,OAAA,CAAChB,GAAG;IAAC2E,EAAE,EAAE;MACPC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAEzB,WAAW;MACnB0B,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE1B,UAAU;MACpB2B,EAAE,EAAE,MAAM;MACVC,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEAtE,OAAA;MACEuE,GAAG,EAAExC,QAAS;MACdyC,GAAG,EAAEpD,KAAK,CAACjB,QAAS;MACpBsE,KAAK,EAAE;QACLX,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACda,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE;MACV,CAAE;MACFC,OAAO,EAAEnC,gBAAiB;MAC1BoC,IAAI;MACJC,KAAK;MACLC,WAAW;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,EAGD,CAAC3D,SAAS,iBACTxB,OAAA,CAAChB,GAAG;MAAC2E,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBwB,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,uBAAuB;QAClCC,MAAM,EAAE;MACV,CAAE;MAAAjB,QAAA,eACAtE,OAAA,CAACd,UAAU;QACT0F,OAAO,EAAEnC,gBAAiB;QAC1BkB,EAAE,EAAE;UACFM,OAAO,EAAE,oBAAoB;UAC7BuB,KAAK,EAAE,OAAO;UACd,SAAS,EAAE;YAAEvB,OAAO,EAAE;UAAqB;QAC7C,CAAE;QAAAK,QAAA,eAEFtE,OAAA,CAACH,aAAa;UAAC8D,EAAE,EAAE;YAAE8B,QAAQ,EAAE;UAAG;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGDnF,OAAA,CAAChB,GAAG;MAAC2E,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB8B,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,GAAG;QACXzB,OAAO,EAAE,MAAM;QACf0B,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,CAAC;QACNN,MAAM,EAAE;MACV,CAAE;MAAAjB,QAAA,gBAEAtE,OAAA,CAAChB,GAAG;QAAC2E,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EtE,OAAA,CAACb,MAAM;UACLqF,GAAG,EAAEpD,KAAK,CAACZ,MAAM,CAACE,MAAO;UACzBiD,EAAE,EAAE;YACFG,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACViC,MAAM,EAAE,iBAAiB;YACzBnB,MAAM,EAAE;UACV;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACD/D,KAAK,CAACZ,MAAM,CAACG,QAAQ,iBACpBX,OAAA,CAAChB,GAAG;UAAC2E,EAAE,EAAE;YACPM,OAAO,EAAE,SAAS;YAClB8B,YAAY,EAAE,KAAK;YACnBjC,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVK,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB4B,EAAE,EAAE,CAAC,CAAC;YACNF,MAAM,EAAE;UACV,CAAE;UAAAxB,QAAA,eACAtE,OAAA,CAACf,UAAU;YAAC0E,EAAE,EAAE;cAAE6B,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,EAAE;cAAEQ,UAAU,EAAE;YAAO,CAAE;YAAA3B,QAAA,EAAC;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNnF,OAAA,CAAChB,GAAG;QAAC2E,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EtE,OAAA,CAACd,UAAU;UACT0F,OAAO,EAAElC,UAAW;UACpBiB,EAAE,EAAE;YACF6B,KAAK,EAAE9D,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCuC,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,EAED5C,OAAO,gBAAG1B,OAAA,CAACT,YAAY;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGnF,OAAA,CAACR,kBAAkB;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbnF,OAAA,CAACf,UAAU;UAAC0E,EAAE,EAAE;YAAE6B,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAI,CAAE;UAAA1B,QAAA,EACvDzD,KAAK,GAAG,IAAI,GAAG,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEqF,OAAO,CAAC,CAAC,CAAC,GAAG,GAAGrF;QAAK;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNnF,OAAA,CAAChB,GAAG;QAAC2E,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EtE,OAAA,CAACd,UAAU;UACT0F,OAAO,EAAE5B,aAAc;UACvBW,EAAE,EAAE;YACF6B,KAAK,EAAE,OAAO;YACdvB,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,eAEFtE,OAAA,CAACP,qBAAqB;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbnF,OAAA,CAACf,UAAU;UAAC0E,EAAE,EAAE;YAAE6B,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAI,CAAE;UAAA1B,QAAA,EACvDlD,KAAK,CAACR,KAAK,CAACE;QAAQ;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNnF,OAAA,CAAChB,GAAG;QAAC2E,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EtE,OAAA,CAACd,UAAU;UACT0F,OAAO,EAAEhC,UAAW;UACpBe,EAAE,EAAE;YACF6B,KAAK,EAAE5D,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCqC,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,EAED1C,OAAO,gBAAG5B,OAAA,CAACL,YAAY;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGnF,OAAA,CAACN,kBAAkB;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbnF,OAAA,CAACf,UAAU;UAAC0E,EAAE,EAAE;YAAE6B,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAI,CAAE;UAAA1B,QAAA,EAAC;QAE3D;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNnF,OAAA,CAAChB,GAAG;QAAC2E,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EtE,OAAA,CAACd,UAAU;UACT0F,OAAO,EAAE/B,WAAY;UACrBc,EAAE,EAAE;YACF6B,KAAK,EAAE,OAAO;YACdvB,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,eAEFtE,OAAA,CAACJ,SAAS;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbnF,OAAA,CAACf,UAAU;UAAC0E,EAAE,EAAE;YAAE6B,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAI,CAAE;UAAA1B,QAAA,EACvDlD,KAAK,CAACR,KAAK,CAACG;QAAM;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA,CAAChB,GAAG;MAAC2E,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB+B,MAAM,EAAE,EAAE;QACVN,IAAI,EAAE,EAAE;QACRK,KAAK,EAAE,EAAE;QACTH,MAAM,EAAE;MACV,CAAE;MAAAjB,QAAA,gBAEAtE,OAAA,CAACf,UAAU;QAAC0E,EAAE,EAAE;UACd6B,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZU,EAAE,EAAE;QACN,CAAE;QAAA7B,QAAA,GACCrB,UAAU,CAAC7B,KAAK,CAACJ,UAAU,CAAC,EAAC,UAAG,EAACI,KAAK,CAACH,QAAQ;MAAA;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGbnF,OAAA,CAACf,UAAU;QAAC0E,EAAE,EAAE;UACd6B,KAAK,EAAE,OAAO;UACdS,UAAU,EAAE,MAAM;UAClBR,QAAQ,EAAE,EAAE;UACZU,EAAE,EAAE,CAAC;UACLC,UAAU,EAAE;QACd,CAAE;QAAA9B,QAAA,EACClD,KAAK,CAACf;MAAK;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGbnF,OAAA,CAACf,UAAU;QAAC0E,EAAE,EAAE;UACd6B,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZU,EAAE,EAAE,CAAC;UACLC,UAAU,EAAE,GAAG;UACflC,OAAO,EAAE,aAAa;UACtBmC,eAAe,EAAE,CAAC;UAClBC,eAAe,EAAE,UAAU;UAC3BjC,QAAQ,EAAE;QACZ,CAAE;QAAAC,QAAA,EACClD,KAAK,CAACd;MAAW;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGbnF,OAAA,CAAChB,GAAG;QAAC2E,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE2B,GAAG,EAAE,CAAC;UAAEU,QAAQ,EAAE;QAAO,CAAE;QAAAjC,QAAA,EACpDlD,KAAK,CAACb,IAAI,CAACiG,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACzB1G,OAAA,CAACZ,IAAI;UAEHuH,KAAK,EAAE,IAAIF,GAAG,EAAG;UACjBG,IAAI,EAAC,OAAO;UACZjD,EAAE,EAAE;YACFM,OAAO,EAAE,0BAA0B;YACnCuB,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,EAAE;YACZ5B,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;cAAEI,OAAO,EAAE;YAA2B;UACnD;QAAE,GATGyC,KAAK;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAA5D,EAAA,CA9RSJ,SAAS;EAAA,QAMF9B,QAAQ,EACLC,aAAa;AAAA;AAAAuH,EAAA,GAPvB1F,SAAS;AA+RlB,eAAe,SAAS2F,SAASA,CAAA,EAAG;EAAAC,GAAA;EAClC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpI,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACqI,MAAM,CAAC,GAAGrI,QAAQ,CAACoB,YAAY,CAAC;;EAEvC;EACAlB,SAAS,CAAC,MAAM;IACd,MAAMoI,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIL,iBAAiB,GAAGE,MAAM,CAACI,MAAM,GAAG,CAAC,EAAE;QACzDL,oBAAoB,CAACtE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIyE,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIL,iBAAiB,GAAG,CAAC,EAAE;QAChDC,oBAAoB,CAACtE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC;IACF,CAAC;IAED4E,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEL,YAAY,CAAC;IAC9C,OAAO,MAAMI,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEN,YAAY,CAAC;EAChE,CAAC,EAAE,CAACH,iBAAiB,EAAEE,MAAM,CAACI,MAAM,CAAC,CAAC;EAEtC,oBACEtH,OAAA,CAAChB,GAAG;IAAC2E,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfQ,QAAQ,EAAE,QAAQ;MAClBT,QAAQ,EAAE;IACZ,CAAE;IAAAU,QAAA,GACC4C,MAAM,CAACV,GAAG,CAAC,CAACpF,KAAK,EAAEsF,KAAK,kBACvB1G,OAAA,CAAChB,GAAG;MAEF2E,EAAE,EAAE;QACFC,QAAQ,EAAE,UAAU;QACpBwB,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPvB,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACdyB,SAAS,EAAE,cAAc,CAACoB,KAAK,GAAGM,iBAAiB,IAAI,GAAG,IAAI;QAC9DU,UAAU,EAAE;MACd,CAAE;MAAApD,QAAA,eAEFtE,OAAA,CAACmB,SAAS;QACRC,KAAK,EAAEA,KAAM;QACbC,QAAQ,EAAEqF,KAAK,KAAKM;MAAkB;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IAAC,GAdG/D,KAAK,CAAClB,EAAE;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeV,CACN,CAAC,eAGFnF,OAAA,CAAChB,GAAG;MAAC2E,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBwB,GAAG,EAAE,EAAE;QACPM,KAAK,EAAE,EAAE;QACTzB,OAAO,EAAE,oBAAoB;QAC7B8B,YAAY,EAAE,CAAC;QACf4B,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLrC,MAAM,EAAE;MACV,CAAE;MAAAjB,QAAA,eACAtE,OAAA,CAACf,UAAU;QAAC0E,EAAE,EAAE;UAAE6B,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAAAnB,QAAA,GAC9C0C,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACE,MAAM,CAACI,MAAM;MAAA;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC4B,GAAA,CA7DuBD,SAAS;AAAAe,GAAA,GAATf,SAAS;AAAA,IAAAD,EAAA,EAAAgB,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}