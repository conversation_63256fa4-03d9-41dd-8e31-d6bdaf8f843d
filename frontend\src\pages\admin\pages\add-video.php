<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Tambah Video</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Form Tambah Video</h6>
        </div>
        <div class="card-body">
            <form id="addVideoForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="title">Judul Video <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="title" name="title" required>
                </div>
                
                <div class="form-group">
                    <label for="description">Deskripsi Singkat <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    <small class="text-muted">Deskripsi singkat yang akan ditampilkan di halaman utama (maksimal 200 karakter)</small>
                </div>
                
                <div class="form-group">
                    <label for="content">Konten Lengkap</label>
                    <textarea class="form-control" id="content" name="content" rows="6"></textarea>
                    <small class="text-muted">Konten lengkap yang akan ditampilkan di halaman detail video</small>
                </div>
                
                <div class="form-group">
                    <label for="youtube_url">URL YouTube <span class="text-danger">*</span></label>
                    <input type="url" class="form-control" id="youtube_url" name="youtube_url" 
                           placeholder="https://www.youtube.com/watch?v=XXXXXXXXXXX" required>
                    <small class="text-muted">Masukkan URL video YouTube (contoh: https://www.youtube.com/watch?v=XXXXXXXXXXX)</small>
                </div>
                
                <div class="form-group">
                    <label for="thumbnail">Thumbnail Video</label>
                    <div class="custom-file">
                        <input type="file" class="custom-file-input" id="thumbnail" name="thumbnail" accept="image/*">
                        <label class="custom-file-label" for="thumbnail">Pilih file...</label>
                    </div>
                    <small class="text-muted">Jika tidak diisi, thumbnail akan diambil otomatis dari YouTube</small>
                </div>
                
                <div class="form-group">
                    <label for="category">Kategori</label>
                    <select class="form-control" id="category" name="category">
                        <option value="Umum">Umum</option>
                        <option value="Teknologi">Teknologi</option>
                        <option value="Bisnis">Bisnis</option>
                        <option value="Olahraga">Olahraga</option>
                        <option value="Hiburan">Hiburan</option>
                        <option value="Politik">Politik</option>
                        <option value="Kesehatan">Kesehatan</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="tags">Tags</label>
                    <input type="text" class="form-control" id="tags" name="tags" placeholder="tag1, tag2, tag3">
                    <small class="text-muted">Pisahkan tag dengan koma</small>
                </div>
                
                <div class="form-group">
                    <label for="duration">Durasi (menit:detik)</label>
                    <input type="text" class="form-control" id="duration" name="duration" placeholder="03:45">
                    <small class="text-muted">Format: mm:ss (contoh: 03:45). Jika tidak diisi, durasi akan diambil dari YouTube</small>
                </div>
                
                <div class="form-group">
                    <label for="status">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="featured" name="featured" value="1">
                        <label class="custom-control-label" for="featured">Tampilkan di Featured</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <div id="preview-container" class="mb-3" style="display: none;">
                        <label>Preview YouTube:</label>
                        <div class="embed-responsive embed-responsive-16by9">
                            <iframe id="youtube-preview" class="embed-responsive-item" src="" allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">Simpan Video</button>
                <a href="?page=videos" class="btn btn-secondary">Batal</a>
            </form>
        </div>
    </div>
</div>

<script>
// Extract YouTube ID from URL
function getYouTubeId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}

// Update YouTube preview when URL changes
document.getElementById('youtube_url').addEventListener('input', function() {
    const youtubeUrl = this.value;
    const youtubeId = getYouTubeId(youtubeUrl);
    const previewContainer = document.getElementById('preview-container');
    const youtubePreview = document.getElementById('youtube-preview');
    
    if (youtubeId) {
        youtubePreview.src = `https://www.youtube.com/embed/${youtubeId}`;
        previewContainer.style.display = 'block';
    } else {
        previewContainer.style.display = 'none';
    }
});

// Show filename in custom file input
document.getElementById('thumbnail').addEventListener('change', function() {
    const fileName = this.files[0]?.name || 'Pilih file...';
    const label = document.querySelector('label[for="thumbnail"]');
    label.textContent = fileName;
});

// Form submission
document.getElementById('addVideoForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Validate YouTube URL
    const youtubeUrl = document.getElementById('youtube_url').value;
    const youtubeId = getYouTubeId(youtubeUrl);
    if (!youtubeId) {
        Swal.fire({
            icon: 'error',
            title: 'URL YouTube Tidak Valid',
            text: 'Masukkan URL YouTube yang valid (contoh: https://www.youtube.com/watch?v=XXXXXXXXXXX)'
        });
        return;
    }
    
    // Create FormData
    const formData = new FormData(this);
    formData.append('action', 'add_video');
    formData.append('youtube_id', youtubeId);
    
    try {
        // Show loading
        Swal.fire({
            title: 'Menyimpan...',
            text: 'Mohon tunggu sebentar',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        // Send request
        const response = await fetch('api.php', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: 'Video berhasil ditambahkan',
                confirmButtonText: 'OK'
            }).then(() => {
                window.location.href = '?page=videos';
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: data.message || 'Terjadi kesalahan saat menyimpan video'
            });
        }
    } catch (error) {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Gagal!',
            text: 'Terjadi kesalahan saat menyimpan video'
        });
    }
});
</script>
