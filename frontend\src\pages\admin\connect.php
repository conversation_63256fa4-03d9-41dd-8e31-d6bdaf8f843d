<?php
// Database connection configuration
$host = 'localhost';
$dbname = 'react_news';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Function to get database connection
function getConnection() {
    global $pdo;
    return $pdo;
}

// Function to send JSON response
function sendJsonResponse($data) {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
    
    echo json_encode($data);
    exit();
}

// Function to get setting value
function getSetting($key, $default = null) {
    try {
        $pdo = getConnection();
        
        // Try key-value structure first
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        
        if ($result) {
            return $result['setting_value'];
        }
        
        // Try direct column structure
        $stmt = $pdo->prepare("SELECT $key FROM settings ORDER BY id DESC LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result && isset($result[$key])) {
            return $result[$key];
        }
        
        return $default;
    } catch (PDOException $e) {
        error_log("Error getting setting $key: " . $e->getMessage());
        return $default;
    }
}

// Function to set setting value
function setSetting($key, $value) {
    try {
        $pdo = getConnection();
        
        // Try key-value structure first
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        $result = $stmt->execute([$key, $value, $value]);
        
        if (!$result) {
            // Try direct column structure
            $stmt = $pdo->prepare("UPDATE settings SET $key = ? WHERE id = 1");
            $result = $stmt->execute([$value]);
        }
        
        return $result;
    } catch (PDOException $e) {
        error_log("Error setting $key: " . $e->getMessage());
        return false;
    }
}

// Function to get all settings
function getAllSettings() {
    try {
        $pdo = getConnection();
        
        // Try key-value structure first
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings");
        $stmt->execute();
        $results = $stmt->fetchAll();
        
        if ($results) {
            $settings = [];
            foreach ($results as $row) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
            return $settings;
        }
        
        // Try direct column structure
        $stmt = $pdo->prepare("SELECT * FROM settings ORDER BY id DESC LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch();
        
        return $result ?: [];
    } catch (PDOException $e) {
        error_log("Error getting all settings: " . $e->getMessage());
        return [];
    }
}
?>
