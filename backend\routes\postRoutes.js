// Routing untuk Berita
const express = require('express');
const router = express.Router();

const postController = require('../controller/postController');
const auth = require('../middleware/auth');
const upload = require('../middleware/upload');



router.get('/posts', postController.getPosts);
router.get('/posts/:id', postController.getPostById);
router.post('/posts', upload.single('image'), postController.createPost);
router.put('/posts/:id', auth, upload.single('image'), postController.updatePost);
router.delete('/posts/:id', auth, postController.deletePost);


// Endpoint untuk mengambil semua berita yang disimpan/bookmarked dari database
router.get('/posts/saved', postController.getSavedPosts);
// Endpoint untuk menyimpan dan menghapus berita ke tabel saved
router.post('/posts/:id/save', postController.savePost);
router.delete('/posts/:id/save', postController.unsavePost);
// Endpoint untuk menambah jumlah share
router.post('/posts/:id/share', postController.incrementShare);

// Video endpoints
router.get('/videos', postController.getVideos);
router.get('/videos/:id', postController.getVideoById);
router.post('/videos/:id/like', postController.toggleVideoLike);
router.post('/videos/:id/share', postController.incrementVideoShare);
router.post('/videos/:id/view', postController.incrementVideoView);

module.exports = router;
