-- Create video tables for video management system
-- Run this in your MySQL database

USE react_news;

-- Create videos table
CREATE TABLE IF NOT EXISTS `videos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text,
  `content` longtext,
  `youtube_url` varchar(500) NOT NULL,
  `youtube_id` varchar(20) NOT NULL,
  `thumbnail` varchar(255) DEFAULT NULL,
  `category` varchar(100) DEFAULT 'Umum',
  `tags` text,
  `duration` varchar(10) DEFAULT '00:00',
  `status` enum('draft','published') DEFAULT 'published',
  `views` int(11) DEFAULT 0,
  `likes` int(11) DEFAULT 0,
  `shares` int(11) DEFAULT 0,
  `comments_count` int(11) DEFAULT 0,
  `featured` tinyint(1) DEFAULT 0,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON>IMARY KEY (`id`),
  K<PERSON>Y `category` (`category`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`),
  KEY `youtube_id` (`youtube_id`),
  KEY `featured` (`featured`),
  FOREIGN KEY (`created_by`) REFERENCES `admin`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create video_likes table for tracking likes by IP
CREATE TABLE IF NOT EXISTS `video_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `video_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `liked_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_video_like` (`video_id`, `ip_address`),
  KEY `video_id` (`video_id`),
  KEY `user_id` (`user_id`),
  KEY `ip_address` (`ip_address`),
  KEY `liked_at` (`liked_at`),
  FOREIGN KEY (`video_id`) REFERENCES `videos`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `admin`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create video_comments table for comments by IP
CREATE TABLE IF NOT EXISTS `video_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `video_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(150) DEFAULT NULL,
  `comment` text NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `parent_id` int(11) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `video_id` (`video_id`),
  KEY `user_id` (`user_id`),
  KEY `ip_address` (`ip_address`),
  KEY `status` (`status`),
  KEY `parent_id` (`parent_id`),
  KEY `created_at` (`created_at`),
  FOREIGN KEY (`video_id`) REFERENCES `videos`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `admin`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`parent_id`) REFERENCES `video_comments`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create video_shares table for tracking shares by IP
CREATE TABLE IF NOT EXISTS `video_shares` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `video_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `platform` varchar(50) DEFAULT 'direct',
  `shared_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `video_id` (`video_id`),
  KEY `user_id` (`user_id`),
  KEY `ip_address` (`ip_address`),
  KEY `platform` (`platform`),
  KEY `shared_at` (`shared_at`),
  FOREIGN KEY (`video_id`) REFERENCES `videos`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `admin`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create video_views table for detailed view tracking
CREATE TABLE IF NOT EXISTS `video_views` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `video_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `watch_duration` int(11) DEFAULT 0,
  `viewed_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `video_id` (`video_id`),
  KEY `user_id` (`user_id`),
  KEY `ip_address` (`ip_address`),
  KEY `viewed_at` (`viewed_at`),
  FOREIGN KEY (`video_id`) REFERENCES `videos`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `admin`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample video data
INSERT INTO `videos` (`title`, `description`, `content`, `youtube_url`, `youtube_id`, `category`, `tags`, `duration`, `status`, `views`, `likes`, `shares`, `comments_count`) VALUES
('Breaking News: Teknologi AI Terbaru', 'Perkembangan teknologi AI yang mengubah dunia digital saat ini', 'Video berita eksklusif tentang perkembangan teknologi AI terbaru yang akan mengubah cara kita bekerja dan berinteraksi dengan teknologi. Simak ulasan lengkap dari para ahli teknologi.', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Teknologi', 'teknologi,ai,digital,inovasi,berita', '02:45', 'published', 1250, 89, 45, 12),

('Olahraga: Final Sepak Bola Spektakuler', 'Highlight pertandingan final yang sangat menegangkan', 'Saksikan highlight pertandingan final sepak bola yang penuh drama. Gol spektakuler di menit terakhir yang mengubah segalanya dan membuat penonton terpukau.', 'https://www.youtube.com/watch?v=ScMzIvxBSi4', 'ScMzIvxBSi4', 'Olahraga', 'olahraga,sepakbola,final,highlight,berita', '03:12', 'published', 2100, 156, 78, 23),

('Kuliner: Resep Masakan Tradisional Nusantara', 'Cara membuat masakan tradisional yang lezat dan mudah', 'Tutorial lengkap cara membuat masakan tradisional Nusantara yang lezat dan mudah dibuat di rumah. Cocok untuk pemula yang ingin belajar memasak makanan tradisional.', 'https://www.youtube.com/watch?v=jNQXAC9IVRw', 'jNQXAC9IVRw', 'Kuliner', 'kuliner,resep,tradisional,masakan,nusantara', '04:30', 'published', 890, 67, 34, 8),

('Politik: Update Terkini Pemerintahan', 'Berita politik terbaru dan analisis mendalam', 'Analisis mendalam tentang kebijakan pemerintah terbaru yang berdampak pada masyarakat. Update politik terkini dengan ulasan dari para pengamat politik.', 'https://www.youtube.com/watch?v=M7lc1UVf-VE', 'M7lc1UVf-VE', 'Politik', 'politik,pemerintah,kebijakan,berita,analisis', '05:15', 'published', 1580, 234, 89, 45);

SELECT 'Video tables created successfully!' as message;
