const db = require('../config/db');

class Video {
  static getAll(callback) {
    db.query(
      `SELECT 
        v.id, v.title, v.description, v.content, v.youtube_url, v.youtube_id, 
        v.thumbnail, v.category, v.tags, v.duration, v.status, v.views, 
        v.likes, v.shares, v.comments_count, v.featured, v.created_at, v.updated_at
      FROM videos v
      WHERE v.status = 'published'
      ORDER BY v.created_at DESC`,
      callback
    );
  }

  static getById(id, callback) {
    db.query(
      `SELECT 
        v.id, v.title, v.description, v.content, v.youtube_url, v.youtube_id, 
        v.thumbnail, v.category, v.tags, v.duration, v.status, v.views, 
        v.likes, v.shares, v.comments_count, v.featured, v.created_at, v.updated_at
      FROM videos v
      WHERE v.id = ?`,
      [id],
      callback
    );
  }

  static incrementViews(id, callback) {
    db.query(
      'UPDATE videos SET views = views + 1 WHERE id = ?',
      [id],
      callback
    );
  }

  static toggleLike(id, ip, callback) {
    // First check if this IP has already liked this video
    db.query(
      'SELECT * FROM video_likes WHERE video_id = ? AND ip_address = ?',
      [id, ip],
      (err, results) => {
        if (err) return callback(err);

        if (results.length > 0) {
          // User already liked this video, so remove the like
          db.query(
            'DELETE FROM video_likes WHERE video_id = ? AND ip_address = ?',
            [id, ip],
            (err) => {
              if (err) return callback(err);
              
              // Decrement likes count
              db.query(
                'UPDATE videos SET likes = likes - 1 WHERE id = ?',
                [id],
                (err) => {
                  if (err) return callback(err);
                  
                  // Get updated likes count
                  db.query(
                    'SELECT likes FROM videos WHERE id = ?',
                    [id],
                    (err, results) => {
                      if (err) return callback(err);
                      callback(null, { liked: false, likes: results[0].likes });
                    }
                  );
                }
              );
            }
          );
        } else {
          // User hasn't liked this video yet, so add the like
          db.query(
            'INSERT INTO video_likes (video_id, ip_address) VALUES (?, ?)',
            [id, ip],
            (err) => {
              if (err) return callback(err);
              
              // Increment likes count
              db.query(
                'UPDATE videos SET likes = likes + 1 WHERE id = ?',
                [id],
                (err) => {
                  if (err) return callback(err);
                  
                  // Get updated likes count
                  db.query(
                    'SELECT likes FROM videos WHERE id = ?',
                    [id],
                    (err, results) => {
                      if (err) return callback(err);
                      callback(null, { liked: true, likes: results[0].likes });
                    }
                  );
                }
              );
            }
          );
        }
      }
    );
  }

  static incrementShares(id, callback) {
    db.query(
      'UPDATE videos SET shares = shares + 1 WHERE id = ?',
      [id],
      (err) => {
        if (err) return callback(err);
        
        // Get updated shares count
        db.query(
          'SELECT shares FROM videos WHERE id = ?',
          [id],
          callback
        );
      }
    );
  }

  static getFeatured(limit = 5, callback) {
    db.query(
      `SELECT 
        v.id, v.title, v.description, v.youtube_url, v.youtube_id, 
        v.thumbnail, v.category, v.tags, v.duration, v.views, 
        v.likes, v.shares, v.comments_count, v.created_at
      FROM videos v
      WHERE v.status = 'published' AND v.featured = 1
      ORDER BY v.created_at DESC
      LIMIT ?`,
      [limit],
      callback
    );
  }

  static getPopular(limit = 5, callback) {
    db.query(
      `SELECT 
        v.id, v.title, v.description, v.youtube_url, v.youtube_id, 
        v.thumbnail, v.category, v.tags, v.duration, v.views, 
        v.likes, v.shares, v.comments_count, v.created_at
      FROM videos v
      WHERE v.status = 'published'
      ORDER BY v.views DESC, v.likes DESC
      LIMIT ?`,
      [limit],
      callback
    );
  }
}

module.exports = Video;
