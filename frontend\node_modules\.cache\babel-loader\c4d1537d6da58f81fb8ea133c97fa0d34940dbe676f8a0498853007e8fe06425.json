{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\VideoPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { Box, Typography, useTheme, useMediaQuery, createTheme, ThemeProvider } from '@mui/material';\nimport VideoFeed from '../../components/VideoFeed';\n\n// Create custom theme function (same as LandingPage)\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst createCustomTheme = kostum => {\n  return createTheme({\n    palette: {\n      primary: {\n        main: kostum.primary_color || '#1976d2'\n      },\n      secondary: {\n        main: kostum.secondary_color || '#dc004e'\n      }\n    },\n    typography: {\n      fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif'\n    }\n  });\n};\n\n// Mobile Video Layout Component\nfunction MobileVideoLayout({\n  kostum,\n  bottomNav,\n  setBottomNav,\n  handleBottomNavChange,\n  navigate,\n  user,\n  isAuthenticated,\n  handleLogout\n}) {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: '#000',\n      // Black background like TikTok\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(VideoFeed, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        bottom: 0,\n        left: 0,\n        right: 0,\n        bgcolor: 'rgba(0, 0, 0, 0.8)',\n        backdropFilter: 'blur(10px)',\n        borderTop: '1px solid rgba(255, 255, 255, 0.1)',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => handleBottomNavChange(0),\n          className: `bottom-nav-item ${bottomNav === 0 ? 'active' : ''}`,\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            cursor: 'pointer',\n            py: 1,\n            px: 2,\n            borderRadius: 1,\n            transition: 'all 0.3s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-home bottom-nav-icon\",\n            style: {\n              color: bottomNav === 0 ? kostum.primary_color : '#fff',\n              fontSize: '20px',\n              marginBottom: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: bottomNav === 0 ? kostum.primary_color : '#fff',\n              fontSize: '10px'\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => handleBottomNavChange(1),\n          className: `bottom-nav-item ${bottomNav === 1 ? 'active' : ''}`,\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            cursor: 'pointer',\n            py: 1,\n            px: 2,\n            borderRadius: 1,\n            transition: 'all 0.3s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-play-circle bottom-nav-icon\",\n            style: {\n              color: bottomNav === 1 ? kostum.primary_color : '#fff',\n              fontSize: '20px',\n              marginBottom: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: bottomNav === 1 ? kostum.primary_color : '#fff',\n              fontSize: '10px'\n            },\n            children: \"Video\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => handleBottomNavChange(2),\n          className: `bottom-nav-item ${bottomNav === 2 ? 'active' : ''}`,\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            cursor: 'pointer',\n            py: 1,\n            px: 2,\n            borderRadius: 1,\n            transition: 'all 0.3s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-search bottom-nav-icon\",\n            style: {\n              color: bottomNav === 2 ? kostum.primary_color : '#fff',\n              fontSize: '20px',\n              marginBottom: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: bottomNav === 2 ? kostum.primary_color : '#fff',\n              fontSize: '10px'\n            },\n            children: \"Cari\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => handleBottomNavChange(3),\n          className: `bottom-nav-item ${bottomNav === 3 ? 'active' : ''}`,\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            cursor: 'pointer',\n            py: 1,\n            px: 2,\n            borderRadius: 1,\n            transition: 'all 0.3s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bookmark bottom-nav-icon\",\n            style: {\n              color: bottomNav === 3 ? kostum.primary_color : '#fff',\n              fontSize: '20px',\n              marginBottom: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: bottomNav === 3 ? kostum.primary_color : '#fff',\n              fontSize: '10px'\n            },\n            children: \"Simpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n}\n\n// Desktop Video Layout Component\n_c = MobileVideoLayout;\nfunction DesktopVideoLayout({\n  kostum,\n  navigate,\n  user,\n  isAuthenticated,\n  handleLogout\n}) {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: '#000',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bgcolor: 'rgba(0, 0, 0, 0.9)',\n        backdropFilter: 'blur(10px)',\n        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n        zIndex: 1000,\n        height: 64\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          height: '100%',\n          px: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            cursor: 'pointer',\n            '&:hover': {\n              opacity: 0.8\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              color: '#fff',\n              fontWeight: 'bold'\n            },\n            children: \"Video Portal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            onClick: () => navigate('/'),\n            sx: {\n              color: '#fff',\n              cursor: 'pointer',\n              '&:hover': {\n                color: kostum.primary_color\n              }\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: kostum.primary_color,\n              cursor: 'pointer',\n              fontWeight: 'bold'\n            },\n            children: \"Video\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                color: '#fff'\n              },\n              children: [\"Hi, \", (user === null || user === void 0 ? void 0 : user.name) || 'User']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              onClick: handleLogout,\n              sx: {\n                color: '#fff',\n                cursor: 'pointer',\n                '&:hover': {\n                  color: kostum.secondary_color\n                }\n              },\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(Typography, {\n            onClick: () => navigate('/auth/login'),\n            sx: {\n              color: '#fff',\n              cursor: 'pointer',\n              '&:hover': {\n                color: kostum.primary_color\n              }\n            },\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 8\n      },\n      children: /*#__PURE__*/_jsxDEV(VideoFeed, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n}\n_c2 = DesktopVideoLayout;\nexport default function VideoPage() {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout,\n    isAuthenticated\n  } = useAuth();\n  const [bottomNav, setBottomNav] = useState(1); // Set to Video tab\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: '',\n    primary_color: '#1976d2',\n    secondary_color: '#dc004e',\n    description: ''\n  });\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n\n  // Load settings from database (same as LandingPage)\n  useEffect(() => {\n    const loadSettings = async () => {\n      try {\n        const response = await fetch('http://localhost:8000/frontend/src/pages/admin/api.php?action=get_settings');\n        const data = await response.json();\n        if (data.success) {\n          let logoPath = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzE5NzZkMiIvPgo8cGF0aCBkPSJNMTIgMTJIMjhWMjhIMTJWMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K';\n          if (data.website_logo && data.website_logo !== '/logo192.png' && data.website_logo !== 'assets/logo.png') {\n            const filename = data.website_logo.split('/').pop();\n            logoPath = `http://localhost:8000/uploads/${filename}`;\n          }\n          setKostum(prev => ({\n            ...prev,\n            logo: logoPath,\n            title: data.website_name || 'Video Portal',\n            primary_color: data.primary_color || '#1976d2',\n            secondary_color: data.secondary_color || '#dc004e',\n            description: data.website_description || 'Portal video terkini'\n          }));\n          document.title = (data.website_name || 'Video Portal') + ' - Video';\n        }\n      } catch (error) {\n        console.error('Failed to load settings:', error);\n      }\n    };\n    loadSettings();\n  }, []);\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Handler untuk bottom nav (same logic as LandingPage)\n  const handleBottomNavChange = newValue => {\n    if (newValue === 0) {\n      navigate('/'); // Go to home\n    } else if (newValue === 1) {\n      setBottomNav(1);\n      // Already on video page\n    } else if (newValue === 2) {\n      // Search functionality\n      console.log('Search clicked from video page');\n    } else if (newValue === 3) {\n      // Saved videos\n      if (isAuthenticated) {\n        console.log('Navigate to saved videos');\n      } else {\n        navigate('/auth/login');\n      }\n    }\n  };\n  const customTheme = createCustomTheme(kostum);\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: customTheme,\n    children: isDesktop ? /*#__PURE__*/_jsxDEV(DesktopVideoLayout, {\n      kostum: kostum,\n      navigate: navigate,\n      user: user,\n      isAuthenticated: isAuthenticated,\n      handleLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(MobileVideoLayout, {\n      kostum: kostum,\n      bottomNav: bottomNav,\n      setBottomNav: setBottomNav,\n      handleBottomNavChange: handleBottomNavChange,\n      navigate: navigate,\n      user: user,\n      isAuthenticated: isAuthenticated,\n      handleLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 359,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoPage, \"+cbwVuCiNxjfhyldXZLpzOy9rvs=\", false, function () {\n  return [useNavigate, useAuth, useTheme, useMediaQuery];\n});\n_c3 = VideoPage;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MobileVideoLayout\");\n$RefreshReg$(_c2, \"DesktopVideoLayout\");\n$RefreshReg$(_c3, \"VideoPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useAuth", "Box", "Typography", "useTheme", "useMediaQuery", "createTheme", "ThemeProvider", "VideoFeed", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "createCustomTheme", "kostum", "palette", "primary", "main", "primary_color", "secondary", "secondary_color", "typography", "fontFamily", "MobileVideoLayout", "bottomNav", "setBottomNav", "handleBottomNavChange", "navigate", "user", "isAuthenticated", "handleLogout", "sx", "minHeight", "bgcolor", "position", "overflow", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "bottom", "left", "right", "<PERSON><PERSON>ilter", "borderTop", "zIndex", "display", "justifyContent", "alignItems", "height", "px", "onClick", "className", "flexDirection", "cursor", "py", "borderRadius", "transition", "style", "color", "fontSize", "marginBottom", "variant", "_c", "DesktopVideoLayout", "top", "borderBottom", "opacity", "fontWeight", "gap", "name", "mt", "_c2", "VideoPage", "_s", "logout", "setKostum", "logo", "title", "description", "theme", "isDesktop", "breakpoints", "up", "loadSettings", "response", "fetch", "data", "json", "success", "logoPath", "website_logo", "filename", "split", "pop", "prev", "website_name", "website_description", "document", "error", "console", "newValue", "log", "customTheme", "_c3", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/VideoPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { \n  Box, \n  Typography, \n  useTheme, \n  useMediaQuery,\n  createTheme,\n  ThemeProvider\n} from '@mui/material';\nimport VideoFeed from '../../components/VideoFeed';\n\n// Create custom theme function (same as LandingPage)\nconst createCustomTheme = (kostum) => {\n  return createTheme({\n    palette: {\n      primary: {\n        main: kostum.primary_color || '#1976d2',\n      },\n      secondary: {\n        main: kostum.secondary_color || '#dc004e',\n      },\n    },\n    typography: {\n      fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    },\n  });\n};\n\n// Mobile Video Layout Component\nfunction MobileVideoLayout({ kostum, bottomNav, setBottomNav, handleBottomNavChange, navigate, user, isAuthenticated, handleLogout }) {\n  return (\n    <Box sx={{ \n      minHeight: '100vh', \n      bgcolor: '#000', // Black background like TikTok\n      position: 'relative',\n      overflow: 'hidden'\n    }}>\n      {/* Video Feed Component */}\n      <VideoFeed />\n\n      {/* Bottom Navigation */}\n      <Box sx={{\n        position: 'fixed',\n        bottom: 0,\n        left: 0,\n        right: 0,\n        bgcolor: 'rgba(0, 0, 0, 0.8)',\n        backdropFilter: 'blur(10px)',\n        borderTop: '1px solid rgba(255, 255, 255, 0.1)',\n        zIndex: 1000\n      }}>\n        <Box sx={{\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        }}>\n          <Box\n            onClick={() => handleBottomNavChange(0)}\n            className={`bottom-nav-item ${bottomNav === 0 ? 'active' : ''}`}\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              cursor: 'pointer',\n              py: 1,\n              px: 2,\n              borderRadius: 1,\n              transition: 'all 0.3s ease'\n            }}\n          >\n            <i className=\"fas fa-home bottom-nav-icon\" style={{ \n              color: bottomNav === 0 ? kostum.primary_color : '#fff',\n              fontSize: '20px',\n              marginBottom: '4px'\n            }}></i>\n            <Typography variant=\"caption\" sx={{ \n              color: bottomNav === 0 ? kostum.primary_color : '#fff',\n              fontSize: '10px'\n            }}>\n              Home\n            </Typography>\n          </Box>\n\n          <Box\n            onClick={() => handleBottomNavChange(1)}\n            className={`bottom-nav-item ${bottomNav === 1 ? 'active' : ''}`}\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              cursor: 'pointer',\n              py: 1,\n              px: 2,\n              borderRadius: 1,\n              transition: 'all 0.3s ease'\n            }}\n          >\n            <i className=\"fas fa-play-circle bottom-nav-icon\" style={{ \n              color: bottomNav === 1 ? kostum.primary_color : '#fff',\n              fontSize: '20px',\n              marginBottom: '4px'\n            }}></i>\n            <Typography variant=\"caption\" sx={{ \n              color: bottomNav === 1 ? kostum.primary_color : '#fff',\n              fontSize: '10px'\n            }}>\n              Video\n            </Typography>\n          </Box>\n\n          <Box\n            onClick={() => handleBottomNavChange(2)}\n            className={`bottom-nav-item ${bottomNav === 2 ? 'active' : ''}`}\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              cursor: 'pointer',\n              py: 1,\n              px: 2,\n              borderRadius: 1,\n              transition: 'all 0.3s ease'\n            }}\n          >\n            <i className=\"fas fa-search bottom-nav-icon\" style={{ \n              color: bottomNav === 2 ? kostum.primary_color : '#fff',\n              fontSize: '20px',\n              marginBottom: '4px'\n            }}></i>\n            <Typography variant=\"caption\" sx={{ \n              color: bottomNav === 2 ? kostum.primary_color : '#fff',\n              fontSize: '10px'\n            }}>\n              Cari\n            </Typography>\n          </Box>\n\n          <Box\n            onClick={() => handleBottomNavChange(3)}\n            className={`bottom-nav-item ${bottomNav === 3 ? 'active' : ''}`}\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              cursor: 'pointer',\n              py: 1,\n              px: 2,\n              borderRadius: 1,\n              transition: 'all 0.3s ease'\n            }}\n          >\n            <i className=\"fas fa-bookmark bottom-nav-icon\" style={{ \n              color: bottomNav === 3 ? kostum.primary_color : '#fff',\n              fontSize: '20px',\n              marginBottom: '4px'\n            }}></i>\n            <Typography variant=\"caption\" sx={{ \n              color: bottomNav === 3 ? kostum.primary_color : '#fff',\n              fontSize: '10px'\n            }}>\n              Simpan\n            </Typography>\n          </Box>\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\n// Desktop Video Layout Component\nfunction DesktopVideoLayout({ kostum, navigate, user, isAuthenticated, handleLogout }) {\n  return (\n    <Box sx={{ \n      minHeight: '100vh', \n      bgcolor: '#000',\n      display: 'flex',\n      flexDirection: 'column'\n    }}>\n      {/* Top Navigation Bar */}\n      <Box sx={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bgcolor: 'rgba(0, 0, 0, 0.9)',\n        backdropFilter: 'blur(10px)',\n        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n        zIndex: 1000,\n        height: 64\n      }}>\n        <Box sx={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          height: '100%',\n          px: 3\n        }}>\n          {/* Logo */}\n          <Box \n            onClick={() => navigate('/')}\n            sx={{ \n              display: 'flex', \n              alignItems: 'center', \n              cursor: 'pointer',\n              '&:hover': { opacity: 0.8 }\n            }}\n          >\n            <Typography variant=\"h6\" sx={{ color: '#fff', fontWeight: 'bold' }}>\n              Video Portal\n            </Typography>\n          </Box>\n\n          {/* Navigation Menu */}\n          <Box sx={{ display: 'flex', gap: 3 }}>\n            <Typography \n              onClick={() => navigate('/')}\n              sx={{ \n                color: '#fff', \n                cursor: 'pointer',\n                '&:hover': { color: kostum.primary_color }\n              }}\n            >\n              Home\n            </Typography>\n            <Typography \n              sx={{ \n                color: kostum.primary_color, \n                cursor: 'pointer',\n                fontWeight: 'bold'\n              }}\n            >\n              Video\n            </Typography>\n          </Box>\n\n          {/* User Menu */}\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            {isAuthenticated ? (\n              <>\n                <Typography sx={{ color: '#fff' }}>\n                  Hi, {user?.name || 'User'}\n                </Typography>\n                <Typography \n                  onClick={handleLogout}\n                  sx={{ \n                    color: '#fff', \n                    cursor: 'pointer',\n                    '&:hover': { color: kostum.secondary_color }\n                  }}\n                >\n                  Logout\n                </Typography>\n              </>\n            ) : (\n              <Typography \n                onClick={() => navigate('/auth/login')}\n                sx={{ \n                  color: '#fff', \n                  cursor: 'pointer',\n                  '&:hover': { color: kostum.primary_color }\n                }}\n              >\n                Login\n              </Typography>\n            )}\n          </Box>\n        </Box>\n      </Box>\n\n      {/* Video Feed with top margin */}\n      <Box sx={{ mt: 8 }}>\n        <VideoFeed />\n      </Box>\n    </Box>\n  );\n}\n\nexport default function VideoPage() {\n  const navigate = useNavigate();\n  const { user, logout, isAuthenticated } = useAuth();\n  const [bottomNav, setBottomNav] = useState(1); // Set to Video tab\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: '',\n    primary_color: '#1976d2',\n    secondary_color: '#dc004e',\n    description: ''\n  });\n\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n\n  // Load settings from database (same as LandingPage)\n  useEffect(() => {\n    const loadSettings = async () => {\n      try {\n        const response = await fetch('http://localhost:8000/frontend/src/pages/admin/api.php?action=get_settings');\n        const data = await response.json();\n\n        if (data.success) {\n          let logoPath = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzE5NzZkMiIvPgo8cGF0aCBkPSJNMTIgMTJIMjhWMjhIMTJWMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K';\n\n          if (data.website_logo && data.website_logo !== '/logo192.png' && data.website_logo !== 'assets/logo.png') {\n            const filename = data.website_logo.split('/').pop();\n            logoPath = `http://localhost:8000/uploads/${filename}`;\n          }\n\n          setKostum(prev => ({\n            ...prev,\n            logo: logoPath,\n            title: data.website_name || 'Video Portal',\n            primary_color: data.primary_color || '#1976d2',\n            secondary_color: data.secondary_color || '#dc004e',\n            description: data.website_description || 'Portal video terkini'\n          }));\n\n          document.title = (data.website_name || 'Video Portal') + ' - Video';\n        }\n      } catch (error) {\n        console.error('Failed to load settings:', error);\n      }\n    };\n\n    loadSettings();\n  }, []);\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Handler untuk bottom nav (same logic as LandingPage)\n  const handleBottomNavChange = (newValue) => {\n    if (newValue === 0) {\n      navigate('/'); // Go to home\n    } else if (newValue === 1) {\n      setBottomNav(1);\n      // Already on video page\n    } else if (newValue === 2) {\n      // Search functionality\n      console.log('Search clicked from video page');\n    } else if (newValue === 3) {\n      // Saved videos\n      if (isAuthenticated) {\n        console.log('Navigate to saved videos');\n      } else {\n        navigate('/auth/login');\n      }\n    }\n  };\n\n  const customTheme = createCustomTheme(kostum);\n\n  return (\n    <ThemeProvider theme={customTheme}>\n      {isDesktop ? (\n        <DesktopVideoLayout\n          kostum={kostum}\n          navigate={navigate}\n          user={user}\n          isAuthenticated={isAuthenticated}\n          handleLogout={handleLogout}\n        />\n      ) : (\n        <MobileVideoLayout\n          kostum={kostum}\n          bottomNav={bottomNav}\n          setBottomNav={setBottomNav}\n          handleBottomNavChange={handleBottomNavChange}\n          navigate={navigate}\n          user={user}\n          isAuthenticated={isAuthenticated}\n          handleLogout={handleLogout}\n        />\n      )}\n    </ThemeProvider>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SACEC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,aAAa,EACbC,WAAW,EACXC,aAAa,QACR,eAAe;AACtB,OAAOC,SAAS,MAAM,4BAA4B;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;EACpC,OAAOR,WAAW,CAAC;IACjBS,OAAO,EAAE;MACPC,OAAO,EAAE;QACPC,IAAI,EAAEH,MAAM,CAACI,aAAa,IAAI;MAChC,CAAC;MACDC,SAAS,EAAE;QACTF,IAAI,EAAEH,MAAM,CAACM,eAAe,IAAI;MAClC;IACF,CAAC;IACDC,UAAU,EAAE;MACVC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,SAASC,iBAAiBA,CAAC;EAAET,MAAM;EAAEU,SAAS;EAAEC,YAAY;EAAEC,qBAAqB;EAAEC,QAAQ;EAAEC,IAAI;EAAEC,eAAe;EAAEC;AAAa,CAAC,EAAE;EACpI,oBACEpB,OAAA,CAACR,GAAG;IAAC6B,EAAE,EAAE;MACPC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MAAE;MACjBC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEA1B,OAAA,CAACF,SAAS;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGb9B,OAAA,CAACR,GAAG;MAAC6B,EAAE,EAAE;QACPG,QAAQ,EAAE,OAAO;QACjBO,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRV,OAAO,EAAE,oBAAoB;QAC7BW,cAAc,EAAE,YAAY;QAC5BC,SAAS,EAAE,oCAAoC;QAC/CC,MAAM,EAAE;MACV,CAAE;MAAAV,QAAA,eACA1B,OAAA,CAACR,GAAG;QAAC6B,EAAE,EAAE;UACPgB,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,cAAc;UAC9BC,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE,EAAE;UACVC,EAAE,EAAE;QACN,CAAE;QAAAf,QAAA,gBACA1B,OAAA,CAACR,GAAG;UACFkD,OAAO,EAAEA,CAAA,KAAM1B,qBAAqB,CAAC,CAAC,CAAE;UACxC2B,SAAS,EAAE,mBAAmB7B,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAChEO,EAAE,EAAE;YACFgB,OAAO,EAAE,MAAM;YACfO,aAAa,EAAE,QAAQ;YACvBL,UAAU,EAAE,QAAQ;YACpBM,MAAM,EAAE,SAAS;YACjBC,EAAE,EAAE,CAAC;YACLL,EAAE,EAAE,CAAC;YACLM,YAAY,EAAE,CAAC;YACfC,UAAU,EAAE;UACd,CAAE;UAAAtB,QAAA,gBAEF1B,OAAA;YAAG2C,SAAS,EAAC,6BAA6B;YAACM,KAAK,EAAE;cAChDC,KAAK,EAAEpC,SAAS,KAAK,CAAC,GAAGV,MAAM,CAACI,aAAa,GAAG,MAAM;cACtD2C,QAAQ,EAAE,MAAM;cAChBC,YAAY,EAAE;YAChB;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACP9B,OAAA,CAACP,UAAU;YAAC4D,OAAO,EAAC,SAAS;YAAChC,EAAE,EAAE;cAChC6B,KAAK,EAAEpC,SAAS,KAAK,CAAC,GAAGV,MAAM,CAACI,aAAa,GAAG,MAAM;cACtD2C,QAAQ,EAAE;YACZ,CAAE;YAAAzB,QAAA,EAAC;UAEH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN9B,OAAA,CAACR,GAAG;UACFkD,OAAO,EAAEA,CAAA,KAAM1B,qBAAqB,CAAC,CAAC,CAAE;UACxC2B,SAAS,EAAE,mBAAmB7B,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAChEO,EAAE,EAAE;YACFgB,OAAO,EAAE,MAAM;YACfO,aAAa,EAAE,QAAQ;YACvBL,UAAU,EAAE,QAAQ;YACpBM,MAAM,EAAE,SAAS;YACjBC,EAAE,EAAE,CAAC;YACLL,EAAE,EAAE,CAAC;YACLM,YAAY,EAAE,CAAC;YACfC,UAAU,EAAE;UACd,CAAE;UAAAtB,QAAA,gBAEF1B,OAAA;YAAG2C,SAAS,EAAC,oCAAoC;YAACM,KAAK,EAAE;cACvDC,KAAK,EAAEpC,SAAS,KAAK,CAAC,GAAGV,MAAM,CAACI,aAAa,GAAG,MAAM;cACtD2C,QAAQ,EAAE,MAAM;cAChBC,YAAY,EAAE;YAChB;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACP9B,OAAA,CAACP,UAAU;YAAC4D,OAAO,EAAC,SAAS;YAAChC,EAAE,EAAE;cAChC6B,KAAK,EAAEpC,SAAS,KAAK,CAAC,GAAGV,MAAM,CAACI,aAAa,GAAG,MAAM;cACtD2C,QAAQ,EAAE;YACZ,CAAE;YAAAzB,QAAA,EAAC;UAEH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN9B,OAAA,CAACR,GAAG;UACFkD,OAAO,EAAEA,CAAA,KAAM1B,qBAAqB,CAAC,CAAC,CAAE;UACxC2B,SAAS,EAAE,mBAAmB7B,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAChEO,EAAE,EAAE;YACFgB,OAAO,EAAE,MAAM;YACfO,aAAa,EAAE,QAAQ;YACvBL,UAAU,EAAE,QAAQ;YACpBM,MAAM,EAAE,SAAS;YACjBC,EAAE,EAAE,CAAC;YACLL,EAAE,EAAE,CAAC;YACLM,YAAY,EAAE,CAAC;YACfC,UAAU,EAAE;UACd,CAAE;UAAAtB,QAAA,gBAEF1B,OAAA;YAAG2C,SAAS,EAAC,+BAA+B;YAACM,KAAK,EAAE;cAClDC,KAAK,EAAEpC,SAAS,KAAK,CAAC,GAAGV,MAAM,CAACI,aAAa,GAAG,MAAM;cACtD2C,QAAQ,EAAE,MAAM;cAChBC,YAAY,EAAE;YAChB;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACP9B,OAAA,CAACP,UAAU;YAAC4D,OAAO,EAAC,SAAS;YAAChC,EAAE,EAAE;cAChC6B,KAAK,EAAEpC,SAAS,KAAK,CAAC,GAAGV,MAAM,CAACI,aAAa,GAAG,MAAM;cACtD2C,QAAQ,EAAE;YACZ,CAAE;YAAAzB,QAAA,EAAC;UAEH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN9B,OAAA,CAACR,GAAG;UACFkD,OAAO,EAAEA,CAAA,KAAM1B,qBAAqB,CAAC,CAAC,CAAE;UACxC2B,SAAS,EAAE,mBAAmB7B,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAChEO,EAAE,EAAE;YACFgB,OAAO,EAAE,MAAM;YACfO,aAAa,EAAE,QAAQ;YACvBL,UAAU,EAAE,QAAQ;YACpBM,MAAM,EAAE,SAAS;YACjBC,EAAE,EAAE,CAAC;YACLL,EAAE,EAAE,CAAC;YACLM,YAAY,EAAE,CAAC;YACfC,UAAU,EAAE;UACd,CAAE;UAAAtB,QAAA,gBAEF1B,OAAA;YAAG2C,SAAS,EAAC,iCAAiC;YAACM,KAAK,EAAE;cACpDC,KAAK,EAAEpC,SAAS,KAAK,CAAC,GAAGV,MAAM,CAACI,aAAa,GAAG,MAAM;cACtD2C,QAAQ,EAAE,MAAM;cAChBC,YAAY,EAAE;YAChB;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACP9B,OAAA,CAACP,UAAU;YAAC4D,OAAO,EAAC,SAAS;YAAChC,EAAE,EAAE;cAChC6B,KAAK,EAAEpC,SAAS,KAAK,CAAC,GAAGV,MAAM,CAACI,aAAa,GAAG,MAAM;cACtD2C,QAAQ,EAAE;YACZ,CAAE;YAAAzB,QAAA,EAAC;UAEH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAwB,EAAA,GA9ISzC,iBAAiB;AA+I1B,SAAS0C,kBAAkBA,CAAC;EAAEnD,MAAM;EAAEa,QAAQ;EAAEC,IAAI;EAAEC,eAAe;EAAEC;AAAa,CAAC,EAAE;EACrF,oBACEpB,OAAA,CAACR,GAAG;IAAC6B,EAAE,EAAE;MACPC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfc,OAAO,EAAE,MAAM;MACfO,aAAa,EAAE;IACjB,CAAE;IAAAlB,QAAA,gBAEA1B,OAAA,CAACR,GAAG;MAAC6B,EAAE,EAAE;QACPG,QAAQ,EAAE,OAAO;QACjBgC,GAAG,EAAE,CAAC;QACNxB,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRV,OAAO,EAAE,oBAAoB;QAC7BW,cAAc,EAAE,YAAY;QAC5BuB,YAAY,EAAE,oCAAoC;QAClDrB,MAAM,EAAE,IAAI;QACZI,MAAM,EAAE;MACV,CAAE;MAAAd,QAAA,eACA1B,OAAA,CAACR,GAAG;QAAC6B,EAAE,EAAE;UACPgB,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,eAAe;UAC/BE,MAAM,EAAE,MAAM;UACdC,EAAE,EAAE;QACN,CAAE;QAAAf,QAAA,gBAEA1B,OAAA,CAACR,GAAG;UACFkD,OAAO,EAAEA,CAAA,KAAMzB,QAAQ,CAAC,GAAG,CAAE;UAC7BI,EAAE,EAAE;YACFgB,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBM,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE;cAAEa,OAAO,EAAE;YAAI;UAC5B,CAAE;UAAAhC,QAAA,eAEF1B,OAAA,CAACP,UAAU;YAAC4D,OAAO,EAAC,IAAI;YAAChC,EAAE,EAAE;cAAE6B,KAAK,EAAE,MAAM;cAAES,UAAU,EAAE;YAAO,CAAE;YAAAjC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGN9B,OAAA,CAACR,GAAG;UAAC6B,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAEuB,GAAG,EAAE;UAAE,CAAE;UAAAlC,QAAA,gBACnC1B,OAAA,CAACP,UAAU;YACTiD,OAAO,EAAEA,CAAA,KAAMzB,QAAQ,CAAC,GAAG,CAAE;YAC7BI,EAAE,EAAE;cACF6B,KAAK,EAAE,MAAM;cACbL,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBAAEK,KAAK,EAAE9C,MAAM,CAACI;cAAc;YAC3C,CAAE;YAAAkB,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9B,OAAA,CAACP,UAAU;YACT4B,EAAE,EAAE;cACF6B,KAAK,EAAE9C,MAAM,CAACI,aAAa;cAC3BqC,MAAM,EAAE,SAAS;cACjBc,UAAU,EAAE;YACd,CAAE;YAAAjC,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGN9B,OAAA,CAACR,GAAG;UAAC6B,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEqB,GAAG,EAAE;UAAE,CAAE;UAAAlC,QAAA,EACxDP,eAAe,gBACdnB,OAAA,CAAAE,SAAA;YAAAwB,QAAA,gBACE1B,OAAA,CAACP,UAAU;cAAC4B,EAAE,EAAE;gBAAE6B,KAAK,EAAE;cAAO,CAAE;cAAAxB,QAAA,GAAC,MAC7B,EAAC,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2C,IAAI,KAAI,MAAM;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACb9B,OAAA,CAACP,UAAU;cACTiD,OAAO,EAAEtB,YAAa;cACtBC,EAAE,EAAE;gBACF6B,KAAK,EAAE,MAAM;gBACbL,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE;kBAAEK,KAAK,EAAE9C,MAAM,CAACM;gBAAgB;cAC7C,CAAE;cAAAgB,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA,eACb,CAAC,gBAEH9B,OAAA,CAACP,UAAU;YACTiD,OAAO,EAAEA,CAAA,KAAMzB,QAAQ,CAAC,aAAa,CAAE;YACvCI,EAAE,EAAE;cACF6B,KAAK,EAAE,MAAM;cACbL,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBAAEK,KAAK,EAAE9C,MAAM,CAACI;cAAc;YAC3C,CAAE;YAAAkB,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA,CAACR,GAAG;MAAC6B,EAAE,EAAE;QAAEyC,EAAE,EAAE;MAAE,CAAE;MAAApC,QAAA,eACjB1B,OAAA,CAACF,SAAS;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACiC,GAAA,GAzGQR,kBAAkB;AA2G3B,eAAe,SAASS,SAASA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAMhD,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4B,IAAI;IAAEgD,MAAM;IAAE/C;EAAgB,CAAC,GAAG5B,OAAO,CAAC,CAAC;EACnD,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACgB,MAAM,EAAE+D,SAAS,CAAC,GAAG/E,QAAQ,CAAC;IACnCgF,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACT7D,aAAa,EAAE,SAAS;IACxBE,eAAe,EAAE,SAAS;IAC1B4D,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,KAAK,GAAG7E,QAAQ,CAAC,CAAC;EACxB,MAAM8E,SAAS,GAAG7E,aAAa,CAAC4E,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;;EAE3D;EACArF,SAAS,CAAC,MAAM;IACd,MAAMsF,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,4EAA4E,CAAC;QAC1G,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChB,IAAIC,QAAQ,GAAG,wSAAwS;UAEvT,IAAIH,IAAI,CAACI,YAAY,IAAIJ,IAAI,CAACI,YAAY,KAAK,cAAc,IAAIJ,IAAI,CAACI,YAAY,KAAK,iBAAiB,EAAE;YACxG,MAAMC,QAAQ,GAAGL,IAAI,CAACI,YAAY,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;YACnDJ,QAAQ,GAAG,iCAAiCE,QAAQ,EAAE;UACxD;UAEAhB,SAAS,CAACmB,IAAI,KAAK;YACjB,GAAGA,IAAI;YACPlB,IAAI,EAAEa,QAAQ;YACdZ,KAAK,EAAES,IAAI,CAACS,YAAY,IAAI,cAAc;YAC1C/E,aAAa,EAAEsE,IAAI,CAACtE,aAAa,IAAI,SAAS;YAC9CE,eAAe,EAAEoE,IAAI,CAACpE,eAAe,IAAI,SAAS;YAClD4D,WAAW,EAAEQ,IAAI,CAACU,mBAAmB,IAAI;UAC3C,CAAC,CAAC,CAAC;UAEHC,QAAQ,CAACpB,KAAK,GAAG,CAACS,IAAI,CAACS,YAAY,IAAI,cAAc,IAAI,UAAU;QACrE;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF,CAAC;IAEDf,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMvD,YAAY,GAAGA,CAAA,KAAM;IACzB8C,MAAM,CAAC,CAAC;IACRjD,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;;EAED;EACA,MAAMD,qBAAqB,GAAI4E,QAAQ,IAAK;IAC1C,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAClB3E,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,IAAI2E,QAAQ,KAAK,CAAC,EAAE;MACzB7E,YAAY,CAAC,CAAC,CAAC;MACf;IACF,CAAC,MAAM,IAAI6E,QAAQ,KAAK,CAAC,EAAE;MACzB;MACAD,OAAO,CAACE,GAAG,CAAC,gCAAgC,CAAC;IAC/C,CAAC,MAAM,IAAID,QAAQ,KAAK,CAAC,EAAE;MACzB;MACA,IAAIzE,eAAe,EAAE;QACnBwE,OAAO,CAACE,GAAG,CAAC,0BAA0B,CAAC;MACzC,CAAC,MAAM;QACL5E,QAAQ,CAAC,aAAa,CAAC;MACzB;IACF;EACF,CAAC;EAED,MAAM6E,WAAW,GAAG3F,iBAAiB,CAACC,MAAM,CAAC;EAE7C,oBACEJ,OAAA,CAACH,aAAa;IAAC0E,KAAK,EAAEuB,WAAY;IAAApE,QAAA,EAC/B8C,SAAS,gBACRxE,OAAA,CAACuD,kBAAkB;MACjBnD,MAAM,EAAEA,MAAO;MACfa,QAAQ,EAAEA,QAAS;MACnBC,IAAI,EAAEA,IAAK;MACXC,eAAe,EAAEA,eAAgB;MACjCC,YAAY,EAAEA;IAAa;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,gBAEF9B,OAAA,CAACa,iBAAiB;MAChBT,MAAM,EAAEA,MAAO;MACfU,SAAS,EAAEA,SAAU;MACrBC,YAAY,EAAEA,YAAa;MAC3BC,qBAAqB,EAAEA,qBAAsB;MAC7CC,QAAQ,EAAEA,QAAS;MACnBC,IAAI,EAAEA,IAAK;MACXC,eAAe,EAAEA,eAAgB;MACjCC,YAAY,EAAEA;IAAa;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B;EACF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAEpB;AAACmC,EAAA,CApGuBD,SAAS;EAAA,QACd1E,WAAW,EACcC,OAAO,EAUnCG,QAAQ,EACJC,aAAa;AAAA;AAAAoG,GAAA,GAbT/B,SAAS;AAAA,IAAAV,EAAA,EAAAS,GAAA,EAAAgC,GAAA;AAAAC,YAAA,CAAA1C,EAAA;AAAA0C,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}