import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Avatar,
  Container
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  AdminPanelSettings,
  Home
} from '@mui/icons-material';

const AdminLogin = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Check if already logged in
  useEffect(() => {
    const adminAuth = localStorage.getItem('admin_auth');
    if (adminAuth) {
      try {
        const authData = JSON.parse(adminAuth);
        if (authData.isAuthenticated) {
          // Already logged in, redirect to admin dashboard
          window.location.href = 'http://localhost:3000/admin/dashboard';
        }
      } catch (e) {
        // Invalid auth data, clear it
        localStorage.removeItem('admin_auth');
      }
    }
  }, [navigate]);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validation
    if (!formData.email || !formData.password) {
      setError('Email dan password harus diisi');
      setLoading(false);
      return;
    }

    // Simple admin login check
    if (formData.email === '<EMAIL>' && formData.password === 'admin123') {
      // Store admin auth data
      const authData = {
        isAuthenticated: true,
        user: {
          id: 1,
          email: '<EMAIL>',
          name: 'Administrator',
          role: 'admin'
        },
        loginTime: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
      };
      
      localStorage.setItem('admin_auth', JSON.stringify(authData));
      
      // Show success message
      setError('');
      
      // Redirect to admin dashboard with clean URL
      setTimeout(() => {
        window.location.href = 'http://localhost:3000/admin/dashboard';
      }, 500);
      
    } else {
      setError('Email atau password salah. Gunakan: <EMAIL> / admin123');
    }
    
    setLoading(false);
  };

  const goToHome = () => {
    navigate('/');
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      backgroundColor: '#f8fafc',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      p: { xs: 2, sm: 3, md: 4 },
      backgroundImage: `
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(239, 68, 68, 0.1) 0%, transparent 50%)
      `
    }}>
      <Container maxWidth="sm">
        <Card sx={{
          borderRadius: { xs: 3, md: 4 },
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          border: '1px solid rgba(0, 0, 0, 0.05)',
          backgroundColor: '#ffffff',
          overflow: 'hidden'
        }}>
          <CardContent sx={{ p: { xs: 4, sm: 5, md: 6 } }}>
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: { xs: 4, md: 5 } }}>
              <IconButton 
                onClick={goToHome}
                sx={{ 
                  position: 'absolute', 
                  top: { xs: 16, md: 20 }, 
                  left: { xs: 16, md: 20 },
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.08)'
                  }
                }}
              >
                <Home />
              </IconButton>
              
              <Avatar sx={{ 
                width: { xs: 64, md: 80 }, 
                height: { xs: 64, md: 80 }, 
                mx: 'auto', 
                mb: { xs: 3, md: 4 },
                bgcolor: 'error.main',
                boxShadow: '0 8px 32px rgba(239, 68, 68, 0.3)'
              }}>
                <AdminPanelSettings fontSize="large" />
              </Avatar>
              
              <Typography 
                variant="h3" 
                fontWeight="700" 
                color="text.primary" 
                gutterBottom
                sx={{ 
                  fontSize: { xs: '2rem', md: '2.5rem' },
                  background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                Admin Portal
              </Typography>
              <Typography 
                variant="body1" 
                color="text.secondary"
                sx={{ 
                  fontSize: { xs: '1rem', md: '1.125rem' },
                  fontWeight: 500
                }}
              >
                Masuk ke Dashboard Administrator
              </Typography>
            </Box>

            {/* Error Alert */}
            {error && (
              <Alert 
                severity="error" 
                sx={{ 
                  mb: 4,
                  borderRadius: 2,
                  '& .MuiAlert-message': {
                    fontSize: { xs: '0.875rem', md: '1rem' }
                  }
                }}
              >
                {error}
              </Alert>
            )}

            {/* Login Form */}
            <form onSubmit={handleSubmit}>
              <TextField
                fullWidth
                name="email"
                type="email"
                label="Email Administrator"
                value={formData.email}
                onChange={handleChange}
                required
                sx={{ 
                  mb: 3,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    backgroundColor: 'rgba(0, 0, 0, 0.02)',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)'
                    },
                    '&.Mui-focused': {
                      backgroundColor: '#ffffff'
                    }
                  }
                }}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Email color="action" />
                      </InputAdornment>
                    ),
                  }
                }}
              />

              <TextField
                fullWidth
                name="password"
                type={showPassword ? 'text' : 'password'}
                label="Password"
                value={formData.password}
                onChange={handleChange}
                required
                sx={{ 
                  mb: 4,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    backgroundColor: 'rgba(0, 0, 0, 0.02)',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)'
                    },
                    '&.Mui-focused': {
                      backgroundColor: '#ffffff'
                    }
                  }
                }}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }
                }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                sx={{
                  mb: 4,
                  py: { xs: 2, md: 2.5 },
                  borderRadius: 3,
                  textTransform: 'none',
                  fontSize: { xs: 16, md: 18 },
                  fontWeight: 600,
                  backgroundColor: 'error.main',
                  boxShadow: '0 8px 32px rgba(239, 68, 68, 0.3)',
                  '&:hover': {
                    backgroundColor: 'error.dark',
                    boxShadow: '0 12px 40px rgba(239, 68, 68, 0.4)',
                    transform: 'translateY(-2px)'
                  },
                  '&:disabled': {
                    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                {loading ? 'Memproses...' : 'Masuk ke Dashboard'}
              </Button>
            </form>

            {/* Footer */}
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Akses khusus administrator • Login diperlukan
              </Typography>
              <Box sx={{
                p: 3,
                backgroundColor: 'rgba(59, 130, 246, 0.05)',
                borderRadius: 2,
                border: '1px solid rgba(59, 130, 246, 0.1)'
              }}>
                <Typography variant="caption" color="primary.main" sx={{ fontWeight: 600, display: 'block', mb: 1 }}>
                  Demo Credentials:
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                  Email: <EMAIL>
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                  Password: admin123
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
};

export default AdminLogin;
