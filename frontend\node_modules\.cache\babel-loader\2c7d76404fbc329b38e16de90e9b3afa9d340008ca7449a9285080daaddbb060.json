{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\components\\\\VideoFeed.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, IconButton, Avatar, Chip, useTheme, useMediaQuery } from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction VideoItem({\n  video,\n  isActive,\n  onVideoClick\n}) {\n  _s();\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // TikTok aspect ratio: 9:16 (vertical)\n  const videoHeight = isMobile ? '100vh' : '80vh';\n  const videoWidth = isMobile ? '100vw' : '45vh'; // 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '0',\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '0',\n      disablekb: '1'\n    });\n    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n  };\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n    try {\n      // Try Express API first, fallback to PHP API\n      try {\n        await fetch(`http://localhost:3000/api/v1/videos/${video.id}/view`, {\n          method: 'POST'\n        });\n      } catch (expressError) {\n        // Fallback to PHP API\n        await fetch(`http://localhost:3000/admin/api/increment_video_views?id=${video.id}`, {\n          method: 'POST'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to update view count:', error);\n    }\n  };\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n      try {\n        response = await fetch(`http://localhost:3000/api/v1/videos/${video.id}/like`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Fallback to PHP API with port 8000\n        response = await fetch(`http://localhost:3000/admin/api/toggle_video_like?id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked);\n        setLikes(data.likes);\n      } else {\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n  const handleShare = async () => {\n    console.log('Share video:', video.id);\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n      try {\n        response = await fetch(`http://localhost:3000/api/v1/videos/${video.id}/share`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Fallback to PHP API\n        response = await fetch(`http://localhost:3000/admin/api/increment_video_share?id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares;\n      }\n    } catch (error) {\n      console.error('Failed to update share count:', error);\n    }\n  };\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"iframe\", {\n      ref: iframeRef,\n      src: getYouTubeEmbedUrl(video.youtubeId, isActive),\n      style: {\n        width: '100%',\n        height: '100%',\n        border: 'none',\n        cursor: 'pointer'\n      },\n      onClick: handleVideoClick,\n      allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n      allowFullScreen: true,\n      title: video.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      },\n      onClick: handleVideoClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: video.author.avatar,\n          sx: {\n            width: 48,\n            height: 48,\n            border: '2px solid white',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), video.author.verified && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            bgcolor: '#1976d2',\n            borderRadius: '50%',\n            width: 20,\n            height: 20,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mt: -1,\n            border: '2px solid white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: 10,\n              fontWeight: 'bold'\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleLike,\n          sx: {\n            color: isLiked ? '#e91e63' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isLiked ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleComment,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.comments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSave,\n          sx: {\n            color: isSaved ? '#ffc107' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isSaved ? /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(BookmarkBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: \"Simpan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleShare,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.shares\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.8)',\n          fontSize: 12,\n          mb: 1\n        },\n        children: [formatDate(video.uploadDate), \" \\u2022 \", video.duration]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'white',\n          fontWeight: 'bold',\n          fontSize: 16,\n          mb: 1,\n          lineHeight: 1.3\n        },\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: 14,\n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        },\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: video.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(Chip, {\n          label: `#${tag}`,\n          size: \"small\",\n          sx: {\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            fontSize: 11,\n            height: 24,\n            '&:hover': {\n              bgcolor: 'rgba(255, 255, 255, 0.3)'\n            }\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n}\n\n// Main VideoFeed Component\n_s(VideoItem, \"YfQkD9vK244jX6up3O1KMuen6o4=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = VideoItem;\nexport default function VideoFeed() {\n  _s2();\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        // Try Express API first\n        let response;\n        let data;\n        try {\n          // Use new clean API endpoint\n          response = await fetch('http://localhost:3000/api/v1/videos');\n          data = await response.json();\n        } catch (expressError) {\n          console.log('Express API not available, trying PHP API...');\n          // Fallback to PHP API through admin panel route\n          response = await fetch('http://localhost:3000/admin/api/get_videos');\n          data = await response.json();\n        }\n        if (data.success && Array.isArray(data.data)) {\n          // Convert database video data to component format\n          const dbVideos = data.data.filter(video => video.status === 'published') // Only published videos\n          .map(video => {\n            var _video$content, _video$category;\n            return {\n              id: video.id,\n              videoUrl: video.youtube_url,\n              youtubeId: video.youtube_id || extractYouTubeId(video.youtube_url),\n              title: video.title,\n              description: video.description || ((_video$content = video.content) === null || _video$content === void 0 ? void 0 : _video$content.substring(0, 200)) + '...',\n              tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [((_video$category = video.category) === null || _video$category === void 0 ? void 0 : _video$category.toLowerCase()) || 'video'],\n              author: {\n                name: 'News Reporter',\n                avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                verified: true\n              },\n              stats: {\n                likes: parseInt(video.likes) || 0,\n                comments: parseInt(video.comments_count) || 0,\n                shares: parseInt(video.shares) || 0\n              },\n              uploadDate: video.created_at || new Date().toISOString(),\n              duration: video.duration || '03:00',\n              category: video.category || 'Video'\n            };\n          });\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            setVideos(dbVideos);\n            console.log('Loaded videos from database:', dbVideos.length);\n          } else {\n            console.log('No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.log('Failed to load videos from API, showing empty state');\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadVideos();\n  }, []);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = url => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return match && match[2].length === 11 ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Handle scroll to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add scroll listener if no videos\n\n    const handleScroll = e => {\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n    };\n    window.addEventListener('wheel', handleScroll);\n    return () => window.removeEventListener('wheel', handleScroll);\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white',\n      padding: 3,\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 80,\n        height: 80,\n        borderRadius: '50%',\n        bgcolor: 'rgba(255,255,255,0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(PlayArrowIcon, {\n        sx: {\n          fontSize: 40,\n          opacity: 0.7\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        mb: 1\n      },\n      children: \"Belum Ada Video\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        opacity: 0.7,\n        maxWidth: 300,\n        mb: 4\n      },\n      children: \"Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n      onClick: reloadVideos,\n      sx: {\n        color: 'white',\n        bgcolor: 'rgba(255, 255, 255, 0.1)',\n        '&:hover': {\n          bgcolor: 'rgba(255, 255, 255, 0.2)'\n        },\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-refresh\",\n        style: {\n          fontSize: 20\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      sx: {\n        opacity: 0.5\n      },\n      children: \"Ketuk untuk memuat ulang\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        gap: 4,\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 488,\n    columnNumber: 5\n  }, this);\n\n  // Loading state\n  const LoadingVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': {\n            transform: 'rotate(0deg)'\n          },\n          '100%': {\n            transform: 'rotate(360deg)'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      children: \"Memuat Video...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 599,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative'\n    },\n    children: loading ? /*#__PURE__*/_jsxDEV(LoadingVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 9\n    }, this) : videos.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [videos.map((video, index) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n          transition: 'transform 0.3s ease-in-out'\n        },\n        children: /*#__PURE__*/_jsxDEV(VideoItem, {\n          video: video,\n          isActive: index === currentVideoIndex\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 15\n        }, this)\n      }, video.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 13\n      }, this)), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 20,\n          right: 20,\n          bgcolor: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: 2,\n          px: 2,\n          py: 1,\n          zIndex: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12\n          },\n          children: [currentVideoIndex + 1, \" / \", videos.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(EmptyVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 629,\n    columnNumber: 5\n  }, this);\n}\n_s2(VideoFeed, \"C87+OgytXDlcze02hYwWuiUEmSU=\");\n_c2 = VideoFeed;\nvar _c, _c2;\n$RefreshReg$(_c, \"VideoItem\");\n$RefreshReg$(_c2, \"VideoFeed\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "IconButton", "Avatar", "Chip", "useTheme", "useMediaQuery", "FavoriteIcon", "FavoriteBorderIcon", "ChatBubbleOutlineIcon", "BookmarkBorderIcon", "BookmarkIcon", "ShareIcon", "PlayArrowIcon", "PauseIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoItem", "video", "isActive", "onVideoClick", "_s", "isLiked", "setIsLiked", "isSaved", "setIsSaved", "likes", "setLikes", "stats", "iframeRef", "theme", "isMobile", "breakpoints", "down", "videoHeight", "videoWidth", "getYouTubeEmbedUrl", "videoId", "autoplay", "params", "URLSearchParams", "mute", "controls", "loop", "playlist", "rel", "showinfo", "modestbranding", "iv_load_policy", "fs", "disablekb", "toString", "handleVideoClick", "console", "log", "title", "fetch", "id", "method", "expressError", "error", "handleLike", "wasLiked", "prev", "response", "data", "json", "success", "liked", "handleSave", "handleShare", "shares", "handleComment", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "sx", "position", "height", "width", "max<PERSON><PERSON><PERSON>", "mx", "bgcolor", "display", "alignItems", "justifyContent", "overflow", "children", "ref", "src", "youtubeId", "style", "border", "cursor", "onClick", "allow", "allowFullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "left", "right", "bottom", "zIndex", "flexDirection", "gap", "author", "avatar", "verified", "borderRadius", "mt", "color", "fontSize", "fontWeight", "toFixed", "comments", "mb", "uploadDate", "duration", "lineHeight", "WebkitLineClamp", "WebkitBoxOrient", "description", "flexWrap", "tags", "map", "tag", "index", "label", "size", "_c", "VideoFeed", "_s2", "currentVideoIndex", "setCurrentVideoIndex", "videos", "setVideos", "loading", "setLoading", "loadVideos", "Array", "isArray", "dbVideos", "filter", "status", "_video$content", "_video$category", "videoUrl", "youtube_url", "youtube_id", "extractYouTubeId", "content", "substring", "split", "trim", "category", "toLowerCase", "name", "parseInt", "comments_count", "created_at", "toISOString", "length", "url", "regExp", "match", "handleScroll", "e", "deltaY", "window", "addEventListener", "removeEventListener", "reloadVideos", "location", "reload", "EmptyVideoState", "padding", "textAlign", "opacity", "variant", "className", "disabled", "LoadingVideoState", "borderTop", "animation", "transform", "transition", "px", "py", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/components/VideoFeed.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Avatar,\n  Chip,\n  useTheme,\n  useMediaQuery\n} from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nfunction VideoItem({ video, isActive, onVideoClick }) {\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // TikTok aspect ratio: 9:16 (vertical)\n  const videoHeight = isMobile ? '100vh' : '80vh';\n  const videoWidth = isMobile ? '100vw' : '45vh'; // 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '0',\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '0',\n      disablekb: '1'\n    });\n    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n  };\n\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n\n    try {\n      // Try Express API first, fallback to PHP API\n      try {\n        await fetch(`http://localhost:3000/api/v1/videos/${video.id}/view`, {\n          method: 'POST'\n        });\n      } catch (expressError) {\n        // Fallback to PHP API\n        await fetch(`http://localhost:3000/admin/api/increment_video_views?id=${video.id}`, {\n          method: 'POST'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to update view count:', error);\n    }\n  };\n\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n\n      try {\n        response = await fetch(`http://localhost:3000/api/v1/videos/${video.id}/like`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Fallback to PHP API with port 8000\n        response = await fetch(`http://localhost:3000/admin/api/toggle_video_like?id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked);\n        setLikes(data.likes);\n      } else {\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n\n  const handleShare = async () => {\n    console.log('Share video:', video.id);\n\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n\n      try {\n        response = await fetch(`http://localhost:3000/api/v1/videos/${video.id}/share`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Fallback to PHP API\n        response = await fetch(`http://localhost:3000/admin/api/increment_video_share?id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares;\n      }\n    } catch (error) {\n      console.error('Failed to update share count:', error);\n    }\n  };\n\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n\n  return (\n    <Box sx={{\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden'\n    }}>\n      {/* YouTube Video Iframe */}\n      <iframe\n        ref={iframeRef}\n        src={getYouTubeEmbedUrl(video.youtubeId, isActive)}\n        style={{\n          width: '100%',\n          height: '100%',\n          border: 'none',\n          cursor: 'pointer'\n        }}\n        onClick={handleVideoClick}\n        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n        allowFullScreen\n        title={video.title}\n      />\n\n      {/* Video Overlay for Interaction */}\n      <Box sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      }}\n      onClick={handleVideoClick}\n      />\n\n      {/* Right Side Actions (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      }}>\n        {/* Profile Avatar */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <Avatar\n            src={video.author.avatar}\n            sx={{ \n              width: 48, \n              height: 48, \n              border: '2px solid white',\n              cursor: 'pointer'\n            }}\n          />\n          {video.author.verified && (\n            <Box sx={{\n              bgcolor: '#1976d2',\n              borderRadius: '50%',\n              width: 20,\n              height: 20,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mt: -1,\n              border: '2px solid white'\n            }}>\n              <Typography sx={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>✓</Typography>\n            </Box>\n          )}\n        </Box>\n\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleLike}\n            sx={{\n              color: isLiked ? '#e91e63' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes}\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleComment}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.comments}\n          </Typography>\n        </Box>\n\n        {/* Save Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleSave}\n            sx={{\n              color: isSaved ? '#ffc107' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            Simpan\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleShare}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.shares}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Bottom Info (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      }}>\n        {/* Upload Date and Duration */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.8)', \n          fontSize: 12, \n          mb: 1 \n        }}>\n          {formatDate(video.uploadDate)} • {video.duration}\n        </Typography>\n\n        {/* Title */}\n        <Typography sx={{ \n          color: 'white', \n          fontWeight: 'bold', \n          fontSize: 16, \n          mb: 1,\n          lineHeight: 1.3\n        }}>\n          {video.title}\n        </Typography>\n\n        {/* Description */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.9)', \n          fontSize: 14, \n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        }}>\n          {video.description}\n        </Typography>\n\n        {/* Tags */}\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          {video.tags.map((tag, index) => (\n            <Chip\n              key={index}\n              label={`#${tag}`}\n              size=\"small\"\n              sx={{\n                bgcolor: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                fontSize: 11,\n                height: 24,\n                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' }\n              }}\n            />\n          ))}\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\n// Main VideoFeed Component\nexport default function VideoFeed() {\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        // Try Express API first\n        let response;\n        let data;\n\n        try {\n          // Use new clean API endpoint\n          response = await fetch('http://localhost:3000/api/v1/videos');\n          data = await response.json();\n        } catch (expressError) {\n          console.log('Express API not available, trying PHP API...');\n          // Fallback to PHP API through admin panel route\n          response = await fetch('http://localhost:3000/admin/api/get_videos');\n          data = await response.json();\n        }\n\n        if (data.success && Array.isArray(data.data)) {\n          // Convert database video data to component format\n          const dbVideos = data.data\n            .filter(video => video.status === 'published') // Only published videos\n            .map(video => ({\n              id: video.id,\n              videoUrl: video.youtube_url,\n              youtubeId: video.youtube_id || extractYouTubeId(video.youtube_url),\n              title: video.title,\n              description: video.description || video.content?.substring(0, 200) + '...',\n              tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [video.category?.toLowerCase() || 'video'],\n              author: {\n                name: 'News Reporter',\n                avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                verified: true\n              },\n              stats: {\n                likes: parseInt(video.likes) || 0,\n                comments: parseInt(video.comments_count) || 0,\n                shares: parseInt(video.shares) || 0\n              },\n              uploadDate: video.created_at || new Date().toISOString(),\n              duration: video.duration || '03:00',\n              category: video.category || 'Video'\n            }));\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            setVideos(dbVideos);\n            console.log('Loaded videos from database:', dbVideos.length);\n          } else {\n            console.log('No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.log('Failed to load videos from API, showing empty state');\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadVideos();\n  }, []);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = (url) => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return (match && match[2].length === 11) ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Handle scroll to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add scroll listener if no videos\n\n    const handleScroll = (e) => {\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n    };\n\n    window.addEventListener('wheel', handleScroll);\n    return () => window.removeEventListener('wheel', handleScroll);\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white',\n      padding: 3,\n      textAlign: 'center'\n    }}>\n      <Box sx={{\n        width: 80,\n        height: 80,\n        borderRadius: '50%',\n        bgcolor: 'rgba(255,255,255,0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        mb: 3\n      }}>\n        <PlayArrowIcon sx={{ fontSize: 40, opacity: 0.7 }} />\n      </Box>\n\n      <Typography variant=\"h6\" sx={{ mb: 1 }}>\n        Belum Ada Video\n      </Typography>\n\n      <Typography variant=\"body2\" sx={{ opacity: 0.7, maxWidth: 300, mb: 4 }}>\n        Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.\n      </Typography>\n\n      {/* Refresh Button */}\n      <IconButton\n        onClick={reloadVideos}\n        sx={{\n          color: 'white',\n          bgcolor: 'rgba(255, 255, 255, 0.1)',\n          '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' },\n          mb: 3\n        }}\n      >\n        <i className=\"fas fa-refresh\" style={{ fontSize: 20 }} />\n      </IconButton>\n\n      <Typography variant=\"caption\" sx={{ opacity: 0.5 }}>\n        Ketuk untuk memuat ulang\n      </Typography>\n\n      {/* Placeholder UI Elements */}\n      <Box sx={{\n        display: 'flex',\n        gap: 4,\n        mt: 2\n      }}>\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <FavoriteBorderIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n      </Box>\n    </Box>\n  );\n\n  // Loading state\n  const LoadingVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    }}>\n      <Box sx={{\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': { transform: 'rotate(0deg)' },\n          '100%': { transform: 'rotate(360deg)' }\n        }\n      }} />\n\n      <Typography variant=\"body1\">\n        Memuat Video...\n      </Typography>\n    </Box>\n  );\n\n  return (\n    <Box sx={{\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative'\n    }}>\n      {loading ? (\n        <LoadingVideoState />\n      ) : videos.length > 0 ? (\n        <>\n          {videos.map((video, index) => (\n            <Box\n              key={video.id}\n              sx={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: '100%',\n                transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n                transition: 'transform 0.3s ease-in-out'\n              }}\n            >\n              <VideoItem\n                video={video}\n                isActive={index === currentVideoIndex}\n              />\n            </Box>\n          ))}\n\n          {/* Video Counter */}\n          <Box sx={{\n            position: 'absolute',\n            top: 20,\n            right: 20,\n            bgcolor: 'rgba(0, 0, 0, 0.5)',\n            borderRadius: 2,\n            px: 2,\n            py: 1,\n            zIndex: 4\n          }}>\n            <Typography sx={{ color: 'white', fontSize: 12 }}>\n              {currentVideoIndex + 1} / {videos.length}\n            </Typography>\n          </Box>\n        </>\n      ) : (\n        <EmptyVideoState />\n      )}\n    </Box>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,SAAS,MAAM,2BAA2B;;AAEjD;;AAEA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,SAASA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAACuB,KAAK,CAACU,KAAK,CAACF,KAAK,CAAC;EACrD,MAAMG,SAAS,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMkC,KAAK,GAAG3B,QAAQ,CAAC,CAAC;EACxB,MAAM4B,QAAQ,GAAG3B,aAAa,CAAC0B,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAMC,WAAW,GAAGH,QAAQ,GAAG,OAAO,GAAG,MAAM;EAC/C,MAAMI,UAAU,GAAGJ,QAAQ,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC;;EAEhD;EACA,MAAMK,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,GAAG,KAAK,KAAK;IACxD,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCF,QAAQ,EAAEA,QAAQ,GAAG,GAAG,GAAG,GAAG;MAC9BG,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAEP,OAAO;MACjBQ,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,GAAG;MACbC,cAAc,EAAE,GAAG;MACnBC,cAAc,EAAE,GAAG;MACnBC,EAAE,EAAE,GAAG;MACPC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,OAAO,iCAAiCb,OAAO,IAAIE,MAAM,CAACY,QAAQ,CAAC,CAAC,EAAE;EACxE,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC;IACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEpC,KAAK,CAACqC,KAAK,CAAC;IAE1C,IAAI;MACF;MACA,IAAI;QACF,MAAMC,KAAK,CAAC,uCAAuCtC,KAAK,CAACuC,EAAE,OAAO,EAAE;UAClEC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrB;QACA,MAAMH,KAAK,CAAC,4DAA4DtC,KAAK,CAACuC,EAAE,EAAE,EAAE;UAClFC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,QAAQ,GAAGxC,OAAO;;IAExB;IACAC,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBK,QAAQ,CAACoC,IAAI,IAAIzC,OAAO,GAAGyC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAE/C,IAAI;MACF;MACA,IAAIC,QAAQ;MACZ,IAAIC,IAAI;MAER,IAAI;QACFD,QAAQ,GAAG,MAAMR,KAAK,CAAC,uCAAuCtC,KAAK,CAACuC,EAAE,OAAO,EAAE;UAC7EC,MAAM,EAAE;QACV,CAAC,CAAC;QACFO,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAC9B,CAAC,CAAC,OAAOP,YAAY,EAAE;QACrB;QACAK,QAAQ,GAAG,MAAMR,KAAK,CAAC,wDAAwDtC,KAAK,CAACuC,EAAE,EAAE,EAAE;UACzFC,MAAM,EAAE;QACV,CAAC,CAAC;QACFO,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAC9B;MAEA,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB;QACA5C,UAAU,CAAC0C,IAAI,CAACG,KAAK,CAAC;QACtBzC,QAAQ,CAACsC,IAAI,CAACvC,KAAK,CAAC;MACtB,CAAC,MAAM;QACL;QACAH,UAAU,CAACuC,QAAQ,CAAC;QACpBnC,QAAQ,CAACoC,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;MAClD;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACArC,UAAU,CAACuC,QAAQ,CAAC;MACpBnC,QAAQ,CAACoC,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvB5C,UAAU,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,MAAM8C,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BjB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEpC,KAAK,CAACuC,EAAE,CAAC;IAErC,IAAI;MACF;MACA,IAAIO,QAAQ;MACZ,IAAIC,IAAI;MAER,IAAI;QACFD,QAAQ,GAAG,MAAMR,KAAK,CAAC,uCAAuCtC,KAAK,CAACuC,EAAE,QAAQ,EAAE;UAC9EC,MAAM,EAAE;QACV,CAAC,CAAC;QACFO,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAC9B,CAAC,CAAC,OAAOP,YAAY,EAAE;QACrB;QACAK,QAAQ,GAAG,MAAMR,KAAK,CAAC,4DAA4DtC,KAAK,CAACuC,EAAE,EAAE,EAAE;UAC7FC,MAAM,EAAE;QACV,CAAC,CAAC;QACFO,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAC9B;MAEA,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB;QACAjD,KAAK,CAACU,KAAK,CAAC2C,MAAM,GAAGN,IAAI,CAACM,MAAM;MAClC;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1BnB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEpC,KAAK,CAACuC,EAAE,CAAC;EACnD,CAAC;EAED,MAAMgB,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE,OAAO,aAAa;IACxC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,cAAc;IAClE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,EAAE,CAAC,aAAa;EACjD,CAAC;EAED,oBACEnE,OAAA,CAAChB,GAAG;IAACqF,EAAE,EAAE;MACPC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAEnD,WAAW;MACnBoD,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAEpD,UAAU;MACpBqD,EAAE,EAAE,MAAM;MACVC,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEAhF,OAAA;MACEiF,GAAG,EAAElE,SAAU;MACfmE,GAAG,EAAE5D,kBAAkB,CAAClB,KAAK,CAAC+E,SAAS,EAAE9E,QAAQ,CAAE;MACnD+E,KAAK,EAAE;QACLZ,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACdc,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE;MACV,CAAE;MACFC,OAAO,EAAEjD,gBAAiB;MAC1BkD,KAAK,EAAC,0FAA0F;MAChGC,eAAe;MACfhD,KAAK,EAAErC,KAAK,CAACqC;IAAM;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAGF7F,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBwB,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACTZ,MAAM,EAAE;MACV,CAAE;MACFC,OAAO,EAAEjD;IAAiB;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGF7F,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB0B,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,GAAG;QACXrB,OAAO,EAAE,MAAM;QACfuB,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,CAAC;QACNF,MAAM,EAAE;MACV,CAAE;MAAAlB,QAAA,gBAEAhF,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEuB,aAAa,EAAE,QAAQ;UAAEtB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EhF,OAAA,CAACb,MAAM;UACL+F,GAAG,EAAE9E,KAAK,CAACiG,MAAM,CAACC,MAAO;UACzBjC,EAAE,EAAE;YACFG,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVc,MAAM,EAAE,iBAAiB;YACzBC,MAAM,EAAE;UACV;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACDzF,KAAK,CAACiG,MAAM,CAACE,QAAQ,iBACpBvG,OAAA,CAAChB,GAAG;UAACqF,EAAE,EAAE;YACPM,OAAO,EAAE,SAAS;YAClB6B,YAAY,EAAE,KAAK;YACnBhC,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVK,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB2B,EAAE,EAAE,CAAC,CAAC;YACNpB,MAAM,EAAE;UACV,CAAE;UAAAL,QAAA,eACAhF,OAAA,CAACf,UAAU;YAACoF,EAAE,EAAE;cAAEqC,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,EAAE;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAA5B,QAAA,EAAC;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7F,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEuB,aAAa,EAAE,QAAQ;UAAEtB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EhF,OAAA,CAACd,UAAU;UACTqG,OAAO,EAAExC,UAAW;UACpBsB,EAAE,EAAE;YACFqC,KAAK,EAAElG,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCmE,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,EAEDxE,OAAO,gBAAGR,OAAA,CAACT,YAAY;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7F,OAAA,CAACR,kBAAkB;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACb7F,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEqC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvDpE,KAAK,GAAG,IAAI,GAAG,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEiG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAGjG;QAAK;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN7F,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEuB,aAAa,EAAE,QAAQ;UAAEtB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EhF,OAAA,CAACd,UAAU;UACTqG,OAAO,EAAE7B,aAAc;UACvBW,EAAE,EAAE;YACFqC,KAAK,EAAE,OAAO;YACd/B,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,eAEFhF,OAAA,CAACP,qBAAqB;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACb7F,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEqC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvD5E,KAAK,CAACU,KAAK,CAACgG;QAAQ;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN7F,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEuB,aAAa,EAAE,QAAQ;UAAEtB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EhF,OAAA,CAACd,UAAU;UACTqG,OAAO,EAAEhC,UAAW;UACpBc,EAAE,EAAE;YACFqC,KAAK,EAAEhG,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCiE,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,EAEDtE,OAAO,gBAAGV,OAAA,CAACL,YAAY;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7F,OAAA,CAACN,kBAAkB;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACb7F,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEqC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EAAC;QAE3D;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN7F,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEuB,aAAa,EAAE,QAAQ;UAAEtB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EhF,OAAA,CAACd,UAAU;UACTqG,OAAO,EAAE/B,WAAY;UACrBa,EAAE,EAAE;YACFqC,KAAK,EAAE,OAAO;YACd/B,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,eAEFhF,OAAA,CAACJ,SAAS;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACb7F,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEqC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvD5E,KAAK,CAACU,KAAK,CAAC2C;QAAM;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7F,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB2B,MAAM,EAAE,EAAE;QACVF,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTE,MAAM,EAAE;MACV,CAAE;MAAAlB,QAAA,gBAEAhF,OAAA,CAACf,UAAU;QAACoF,EAAE,EAAE;UACdqC,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE;QACN,CAAE;QAAA/B,QAAA,GACCrB,UAAU,CAACvD,KAAK,CAAC4G,UAAU,CAAC,EAAC,UAAG,EAAC5G,KAAK,CAAC6G,QAAQ;MAAA;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGb7F,OAAA,CAACf,UAAU;QAACoF,EAAE,EAAE;UACdqC,KAAK,EAAE,OAAO;UACdE,UAAU,EAAE,MAAM;UAClBD,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE,CAAC;UACLG,UAAU,EAAE;QACd,CAAE;QAAAlC,QAAA,EACC5E,KAAK,CAACqC;MAAK;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGb7F,OAAA,CAACf,UAAU;QAACoF,EAAE,EAAE;UACdqC,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE,CAAC;UACLG,UAAU,EAAE,GAAG;UACftC,OAAO,EAAE,aAAa;UACtBuC,eAAe,EAAE,CAAC;UAClBC,eAAe,EAAE,UAAU;UAC3BrC,QAAQ,EAAE;QACZ,CAAE;QAAAC,QAAA,EACC5E,KAAK,CAACiH;MAAW;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGb7F,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEwB,GAAG,EAAE,CAAC;UAAEkB,QAAQ,EAAE;QAAO,CAAE;QAAAtC,QAAA,EACpD5E,KAAK,CAACmH,IAAI,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACzB1H,OAAA,CAACZ,IAAI;UAEHuI,KAAK,EAAE,IAAIF,GAAG,EAAG;UACjBG,IAAI,EAAC,OAAO;UACZvD,EAAE,EAAE;YACFM,OAAO,EAAE,0BAA0B;YACnC+B,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,EAAE;YACZpC,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;cAAEI,OAAO,EAAE;YAA2B;UACnD;QAAE,GATG+C,KAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAtF,EAAA,CApWSJ,SAAS;EAAA,QAKFd,QAAQ,EACLC,aAAa;AAAA;AAAAuI,EAAA,GANvB1H,SAAS;AAqWlB,eAAe,SAAS2H,SAASA,CAAA,EAAG;EAAAC,GAAA;EAClC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpJ,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACqJ,MAAM,EAAEC,SAAS,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuJ,OAAO,EAAEC,UAAU,CAAC,GAAGxJ,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAE,SAAS,CAAC,MAAM;IACd,MAAMuJ,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7BD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,IAAInF,QAAQ;QACZ,IAAIC,IAAI;QAER,IAAI;UACF;UACAD,QAAQ,GAAG,MAAMR,KAAK,CAAC,qCAAqC,CAAC;UAC7DS,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,OAAOP,YAAY,EAAE;UACrBN,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3D;UACAU,QAAQ,GAAG,MAAMR,KAAK,CAAC,4CAA4C,CAAC;UACpES,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;QAC9B;QAEA,IAAID,IAAI,CAACE,OAAO,IAAIkF,KAAK,CAACC,OAAO,CAACrF,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5C;UACA,MAAMsF,QAAQ,GAAGtF,IAAI,CAACA,IAAI,CACvBuF,MAAM,CAACtI,KAAK,IAAIA,KAAK,CAACuI,MAAM,KAAK,WAAW,CAAC,CAAC;UAAA,CAC9CnB,GAAG,CAACpH,KAAK;YAAA,IAAAwI,cAAA,EAAAC,eAAA;YAAA,OAAK;cACblG,EAAE,EAAEvC,KAAK,CAACuC,EAAE;cACZmG,QAAQ,EAAE1I,KAAK,CAAC2I,WAAW;cAC3B5D,SAAS,EAAE/E,KAAK,CAAC4I,UAAU,IAAIC,gBAAgB,CAAC7I,KAAK,CAAC2I,WAAW,CAAC;cAClEtG,KAAK,EAAErC,KAAK,CAACqC,KAAK;cAClB4E,WAAW,EAAEjH,KAAK,CAACiH,WAAW,IAAI,EAAAuB,cAAA,GAAAxI,KAAK,CAAC8I,OAAO,cAAAN,cAAA,uBAAbA,cAAA,CAAeO,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAG,KAAK;cAC1E5B,IAAI,EAAEnH,KAAK,CAACmH,IAAI,GAAGnH,KAAK,CAACmH,IAAI,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAAC5B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAAC4B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAAR,eAAA,GAAAzI,KAAK,CAACkJ,QAAQ,cAAAT,eAAA,uBAAdA,eAAA,CAAgBU,WAAW,CAAC,CAAC,KAAI,OAAO,CAAC;cAC5GlD,MAAM,EAAE;gBACNmD,IAAI,EAAE,eAAe;gBACrBlD,MAAM,EAAE,gCAAgC,IAAIlG,KAAK,CAACuC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC9D4D,QAAQ,EAAE;cACZ,CAAC;cACDzF,KAAK,EAAE;gBACLF,KAAK,EAAE6I,QAAQ,CAACrJ,KAAK,CAACQ,KAAK,CAAC,IAAI,CAAC;gBACjCkG,QAAQ,EAAE2C,QAAQ,CAACrJ,KAAK,CAACsJ,cAAc,CAAC,IAAI,CAAC;gBAC7CjG,MAAM,EAAEgG,QAAQ,CAACrJ,KAAK,CAACqD,MAAM,CAAC,IAAI;cACpC,CAAC;cACDuD,UAAU,EAAE5G,KAAK,CAACuJ,UAAU,IAAI,IAAI7F,IAAI,CAAC,CAAC,CAAC8F,WAAW,CAAC,CAAC;cACxD3C,QAAQ,EAAE7G,KAAK,CAAC6G,QAAQ,IAAI,OAAO;cACnCqC,QAAQ,EAAElJ,KAAK,CAACkJ,QAAQ,IAAI;YAC9B,CAAC;UAAA,CAAC,CAAC;;UAEL;UACA,IAAIb,QAAQ,CAACoB,MAAM,GAAG,CAAC,EAAE;YACvB1B,SAAS,CAACM,QAAQ,CAAC;YACnBlG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEiG,QAAQ,CAACoB,MAAM,CAAC;UAC9D,CAAC,MAAM;YACLtH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAC7D2F,SAAS,CAAC,EAAE,CAAC;UACf;QACF,CAAC,MAAM;UACL5F,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE2F,SAAS,CAAC,EAAE,CAAC;QACf;MACF,CAAC,CAAC,OAAOrF,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACAqF,SAAS,CAAC,EAAE,CAAC;MACf,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,gBAAgB,GAAIa,GAAG,IAAK;IAChC,IAAI,CAACA,GAAG,EAAE,OAAO,IAAI;IACrB,MAAMC,MAAM,GAAG,8DAA8D;IAC7E,MAAMC,KAAK,GAAGF,GAAG,CAACE,KAAK,CAACD,MAAM,CAAC;IAC/B,OAAQC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACH,MAAM,KAAK,EAAE,GAAIG,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;EACvE,CAAC;;EAED;EACAjL,SAAS,CAAC,MAAM;IACd,IAAImJ,MAAM,CAAC2B,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;;IAEjC,MAAMI,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,MAAM,GAAG,CAAC,IAAInC,iBAAiB,GAAGE,MAAM,CAAC2B,MAAM,GAAG,CAAC,EAAE;QACzD5B,oBAAoB,CAAChF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIiH,CAAC,CAACC,MAAM,GAAG,CAAC,IAAInC,iBAAiB,GAAG,CAAC,EAAE;QAChDC,oBAAoB,CAAChF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC;IACF,CAAC;IAEDmH,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEJ,YAAY,CAAC;IAC9C,OAAO,MAAMG,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEL,YAAY,CAAC;EAChE,CAAC,EAAE,CAACjC,iBAAiB,EAAEE,MAAM,CAAC2B,MAAM,CAAC,CAAC;;EAEtC;EACA,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzBlC,UAAU,CAAC,IAAI,CAAC;IAChB;IACA+B,MAAM,CAACI,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtB1K,OAAA,CAAChB,GAAG;IAACqF,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfK,OAAO,EAAE,MAAM;MACfuB,aAAa,EAAE,QAAQ;MACvBtB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,MAAM;MACf+B,KAAK,EAAE,OAAO;MACdiE,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE;IACb,CAAE;IAAA5F,QAAA,gBACAhF,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPG,KAAK,EAAE,EAAE;QACTD,MAAM,EAAE,EAAE;QACViC,YAAY,EAAE,KAAK;QACnB7B,OAAO,EAAE,uBAAuB;QAChCC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBiC,EAAE,EAAE;MACN,CAAE;MAAA/B,QAAA,eACAhF,OAAA,CAACH,aAAa;QAACwE,EAAE,EAAE;UAAEsC,QAAQ,EAAE,EAAE;UAAEkE,OAAO,EAAE;QAAI;MAAE;QAAAnF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAEN7F,OAAA,CAACf,UAAU;MAAC6L,OAAO,EAAC,IAAI;MAACzG,EAAE,EAAE;QAAE0C,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,EAAC;IAExC;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb7F,OAAA,CAACf,UAAU;MAAC6L,OAAO,EAAC,OAAO;MAACzG,EAAE,EAAE;QAAEwG,OAAO,EAAE,GAAG;QAAEpG,QAAQ,EAAE,GAAG;QAAEsC,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,EAAC;IAExE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb7F,OAAA,CAACd,UAAU;MACTqG,OAAO,EAAEgF,YAAa;MACtBlG,EAAE,EAAE;QACFqC,KAAK,EAAE,OAAO;QACd/B,OAAO,EAAE,0BAA0B;QACnC,SAAS,EAAE;UAAEA,OAAO,EAAE;QAA2B,CAAC;QAClDoC,EAAE,EAAE;MACN,CAAE;MAAA/B,QAAA,eAEFhF,OAAA;QAAG+K,SAAS,EAAC,gBAAgB;QAAC3F,KAAK,EAAE;UAAEuB,QAAQ,EAAE;QAAG;MAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eAEb7F,OAAA,CAACf,UAAU;MAAC6L,OAAO,EAAC,SAAS;MAACzG,EAAE,EAAE;QAAEwG,OAAO,EAAE;MAAI,CAAE;MAAA7F,QAAA,EAAC;IAEpD;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb7F,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPO,OAAO,EAAE,MAAM;QACfwB,GAAG,EAAE,CAAC;QACNK,EAAE,EAAE;MACN,CAAE;MAAAzB,QAAA,gBAEAhF,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEuB,aAAa,EAAE,QAAQ;UAAEtB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EhF,OAAA,CAACd,UAAU;UACT8L,QAAQ;UACR3G,EAAE,EAAE;YACFqC,KAAK,EAAE,OAAO;YACd/B,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEkG,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA7F,QAAA,eAEFhF,OAAA,CAACR,kBAAkB;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACb7F,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEqC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAEoE,OAAO,EAAE;UAAI,CAAE;UAAA7F,QAAA,EAAC;QAEzE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN7F,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEuB,aAAa,EAAE,QAAQ;UAAEtB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EhF,OAAA,CAACd,UAAU;UACT8L,QAAQ;UACR3G,EAAE,EAAE;YACFqC,KAAK,EAAE,OAAO;YACd/B,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEkG,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA7F,QAAA,eAEFhF,OAAA,CAACP,qBAAqB;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACb7F,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEqC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAEoE,OAAO,EAAE;UAAI,CAAE;UAAA7F,QAAA,EAAC;QAEzE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN7F,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEuB,aAAa,EAAE,QAAQ;UAAEtB,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EhF,OAAA,CAACd,UAAU;UACT8L,QAAQ;UACR3G,EAAE,EAAE;YACFqC,KAAK,EAAE,OAAO;YACd/B,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEkG,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA7F,QAAA,eAEFhF,OAAA,CAACJ,SAAS;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACb7F,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEqC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAEoE,OAAO,EAAE;UAAI,CAAE;UAAA7F,QAAA,EAAC;QAEzE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMoF,iBAAiB,GAAGA,CAAA,kBACxBjL,OAAA,CAAChB,GAAG;IAACqF,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfK,OAAO,EAAE,MAAM;MACfuB,aAAa,EAAE,QAAQ;MACvBtB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,MAAM;MACf+B,KAAK,EAAE;IACT,CAAE;IAAA1B,QAAA,gBACAhF,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPG,KAAK,EAAE,EAAE;QACTD,MAAM,EAAE,EAAE;QACViC,YAAY,EAAE,KAAK;QACnBnB,MAAM,EAAE,iCAAiC;QACzC6F,SAAS,EAAE,iBAAiB;QAC5BC,SAAS,EAAE,yBAAyB;QACpCpE,EAAE,EAAE,CAAC;QACL,iBAAiB,EAAE;UACjB,IAAI,EAAE;YAAEqE,SAAS,EAAE;UAAe,CAAC;UACnC,MAAM,EAAE;YAAEA,SAAS,EAAE;UAAiB;QACxC;MACF;IAAE;MAAA1F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEL7F,OAAA,CAACf,UAAU;MAAC6L,OAAO,EAAC,OAAO;MAAA9F,QAAA,EAAC;IAE5B;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACN;EAED,oBACE7F,OAAA,CAAChB,GAAG;IAACqF,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfQ,QAAQ,EAAE,QAAQ;MAClBT,QAAQ,EAAE;IACZ,CAAE;IAAAU,QAAA,EACCoD,OAAO,gBACNpI,OAAA,CAACiL,iBAAiB;MAAAvF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACnBqC,MAAM,CAAC2B,MAAM,GAAG,CAAC,gBACnB7J,OAAA,CAAAE,SAAA;MAAA8E,QAAA,GACGkD,MAAM,CAACV,GAAG,CAAC,CAACpH,KAAK,EAAEsH,KAAK,kBACvB1H,OAAA,CAAChB,GAAG;QAEFqF,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBwB,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPvB,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACd6G,SAAS,EAAE,cAAc,CAAC1D,KAAK,GAAGM,iBAAiB,IAAI,GAAG,IAAI;UAC9DqD,UAAU,EAAE;QACd,CAAE;QAAArG,QAAA,eAEFhF,OAAA,CAACG,SAAS;UACRC,KAAK,EAAEA,KAAM;UACbC,QAAQ,EAAEqH,KAAK,KAAKM;QAAkB;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC,GAdGzF,KAAK,CAACuC,EAAE;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeV,CACN,CAAC,eAGF7F,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UACPC,QAAQ,EAAE,UAAU;UACpBwB,GAAG,EAAE,EAAE;UACPE,KAAK,EAAE,EAAE;UACTrB,OAAO,EAAE,oBAAoB;UAC7B6B,YAAY,EAAE,CAAC;UACf8E,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACLrF,MAAM,EAAE;QACV,CAAE;QAAAlB,QAAA,eACAhF,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEqC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAA3B,QAAA,GAC9CgD,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACE,MAAM,CAAC2B,MAAM;QAAA;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,eACN,CAAC,gBAEH7F,OAAA,CAAC0K,eAAe;MAAAhF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnB;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACkC,GAAA,CA3SuBD,SAAS;AAAA0D,GAAA,GAAT1D,SAAS;AAAA,IAAAD,EAAA,EAAA2D,GAAA;AAAAC,YAAA,CAAA5D,EAAA;AAAA4D,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}